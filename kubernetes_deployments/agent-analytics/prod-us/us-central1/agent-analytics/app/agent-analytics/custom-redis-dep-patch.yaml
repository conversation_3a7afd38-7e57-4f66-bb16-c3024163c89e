apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: redis
  name: redis
spec:
  template:
    spec:
      containers:
      - name: redis
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
        args:
          - --appendonly
          - "yes"
          - --maxmemory
          - "3865470566"
          - --maxmemory-policy
          - noeviction
          - --save
          - ""