apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-analytics-hpa
spec:
  maxReplicas: 20
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-analytics
  metrics:
  - external:
      metric:
        name: pubsub.googleapis.com|subscription|num_undelivered_messages
        selector:
          matchLabels:
            resource.labels.subscription_id: agent-analytics-subscription
      target:
        type: AverageValue
        averageValue: "24"
    type: External
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80