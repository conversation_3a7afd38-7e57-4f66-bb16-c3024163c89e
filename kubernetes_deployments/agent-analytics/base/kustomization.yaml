apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - deployment.yaml
  - service-account.yaml
  - pvc.yaml
  - hpa.yaml
  - ../../reloader

bases:
  - ../../essentials
  - ../../redis


# Generate config from templates
configMapGenerator:
- name: agent-analytics-configmap
  envs:
  - params
secretGenerator:
  - name: agent-analytics-secrets
    literals:
      - telemetry_key=b48fad60126925dadcfff6622d7b206b1d21359271b01367909f316347f85c31
      - redis_password=""
patches:
  - custom-redis-dep.yaml
  - reloader-patch.yaml

generatorOptions:
  disableNameSuffixHash: true

vars:
- name: GCP_SA
  objref:
    kind: ConfigMap
    name: agent-analytics-configmap
    apiVersion: v1
  fieldref:
    fieldpath: data.GCP_SA

configurations:
- params.yaml
