apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    "configmap.reloader.stakater.com/reload": agent-analytics-configmap
  labels:
    app: agent-analytics
  name: agent-analytics
spec:
  selector:
    matchLabels:
      app: agent-analytics
  template:
    metadata:
      annotations:
        prometheus.io/port: "6666"
        prometheus.io/scrape: "true"
      labels:
        app: agent-analytics
    spec:
      serviceAccountName: agent-analytics
      securityContext:
        fsGroup: 888
      containers:
      - image: backend
        name: backend
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
          requests:
            cpu: "250m"
            memory: 1Gi
        command:
          - /bin/sh
          - /src/runner.sh
          - /src/secdo/app.py
          - agent_analytics
        env:
          - name: REDISCONF_PASSWORD
            valueFrom:
              secretKeyRef:
                key: redis_password
                name: agent-analytics-secrets
          - name: AGENT_ANALYTICS_TELEMETRY_KEY
            valueFrom:
              secretKeyRef:
                key: telemetry_key
                name: agent-analytics-secrets
        envFrom:
          - configMapRef:
              name: agent-analytics-configmap
              optional: false