apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: redis
  name: redis
spec:
  template:
    spec:
      containers:
      - name: redis
        resources:
          limits:
            cpu: "1"
            memory: 2Gi
          requests:
            cpu: "200m"
            memory: 200Mi
        args:
          - --appendonly
          - "yes"
          - --maxmemory
          - "1932735283"
          - --maxmemory-policy
          - noeviction
          - --save
          - ""
        volumeMounts:
          - mountPath: /data
            name: redis-persistent-storage
      volumes:
        - name: redis-persistent-storage
          persistentVolumeClaim:
            claimName: redis-volume