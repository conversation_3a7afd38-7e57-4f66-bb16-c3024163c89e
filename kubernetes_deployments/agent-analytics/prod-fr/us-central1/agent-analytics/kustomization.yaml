apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- app/agent-analytics
- app/custom-metrics

images:
- name: backend
  newName: us.gcr.io/xdr-registry-prod-fr-01/backend
  newTag: master-fips-v3.11.0-4722-g080fb
- name: consul-agent
  newName: us.gcr.io/xdr-registry-prod-fr-01/consul_agent
  newTag: master-v80
- name: custom-metrics
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: mysql
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/mysql
  newTag: 8.0.35
- name: prometheus
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/prometheus
  newTag: v2.47.2
- name: prometheus-adapter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/k8s-prometheus-adapter
  newTag: v1.10.0
- name: redis
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/redis
  newTag: 7.2.4-alpine
- name: reloader
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stakater/reloader
  newTag: v1.0.93
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
