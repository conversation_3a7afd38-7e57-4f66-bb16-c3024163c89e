apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- app/agent-analytics
- app/custom-metrics

images:
- name: backend
  newName: gcr.io/kuna-224012/kuna-xdr-backend
  newTag: master-v3.9.0-5515-g8416ae
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/google/cloud-sdk
  newTag: 483.0.0-alpine
- name: mysql
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/mysql
  newTag: 8.0.39-debian
- name: pod-custom-metrics-stackdriver-adapter
  newName: gcr.io/google-containers/custom-metrics-stackdriver-adapter
  newTag: v0.10.1
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: redis
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/redis
  newTag: 7.2.5-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/stakater/reloader
  newTag: v1.0.93
