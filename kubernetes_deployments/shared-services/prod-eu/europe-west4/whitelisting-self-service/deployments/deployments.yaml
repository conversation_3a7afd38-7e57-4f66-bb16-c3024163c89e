---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wl-self-service
  namespace: wl-self-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wl-self-service
  template:
    metadata:
      labels:
        app: wl-self-service
    spec:
      containers:
        - image: wl-self-service
          imagePullPolicy: Always
          name: wl-self-service
          ports:
          - containerPort: 80
            protocol: TCP
          resources:
            limits:
              cpu: "500m"
              memory: 500Mi
            requests:
              cpu: "350m"
              memory: 300Mi
          readinessProbe:
            failureThreshold: 10
            httpGet:
              path: /
              port: 80
              scheme: HTTP
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: /
              port: 80
              scheme: HTTP
---
apiVersion: apps/v1
kind: Deployment 
metadata:
  name: wl-self-service-backend
  namespace: wl-self-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wl-self-service
      component: backend
  template:
    metadata:
      labels:
        app: wl-self-service
        component: backend
    spec:
      containers:
        - image: wl-self-service-backend
          imagePullPolicy: Always
          name: wl-self-service
          env:
            - name: JIRA_TOKEN
              valueFrom:
                secretKeyRef:
                  key: ss_jira_token
                  name: jira-token
          ports:
          - containerPort: 5001
            protocol: TCP
          resources:
            limits:
              cpu: "500m"
              memory: 500Mi
            requests:
              cpu: "350m"
              memory: 300Mi
          readinessProbe:
            failureThreshold: 10
            httpGet:
              path: /health
              port: 5001
              scheme: HTTP
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: /health
              port: 5001
              scheme: HTTP