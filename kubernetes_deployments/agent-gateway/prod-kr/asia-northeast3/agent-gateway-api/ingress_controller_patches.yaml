apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ************ # agent-gateway-public-api-address
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ************ # broker-address
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      nodeSelector:
        type: prod-kr
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      nodeSelector:
        type: prod-kr