apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ../../../base
- ../../../base/prod_dev_services
- ../../../../reloader
- ../../../base/nginx-controllers
- ../../../base/deployments/opp-gateway
- ../../../base/deployments/opp-telemetry
- opp-gateway-ingress.yaml

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr.kr.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx.kr.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true
  #Some values here are wrong cause it don't want hydra to send alerts to prod channels
  #TODO Change viso IP and slack channel

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=prod
  - GONZO_PROJECT_ID=xdr-agent-gateway-prod-kr-01
  - GONZO_SLACK_CHANNEL='#hydra-dev-alerts'
  - GONZO_REGION=asia-northeast3
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-pub-prod-us-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-us-01
  - HYDRA_PROXY_BUDDY=************ # pb-in
  - HYDRA_PROXY_BUDDY_MAX_CON=0
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON=200
  - HYDRA_PROXY_BUDDY_TIMEOUT=1800s
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr.kr.paloaltonetworks.com
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-prod-kr-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-prod-kr
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-kr-01
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32 # shared-engines FWs
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-prod-kr-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32,*************/32,************/32  # mag-shared FWs & shared-engines FWs
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx.kr.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32 # shared-engines FWs
  - HYDRA_PROXY_BUDDY=************ # pb-in-ws-dns
  name: agent-gateway-api-configmap-xsoar
- behavior: merge
  literals:
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-kr-01
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-prod-us
  - HYDRA_PUBSUB_PROJECT_ID=xdr-agent-gateway-prod-kr-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-kr
  name: opp-telemetry-configmap
- behavior: merge
  literals:
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-kr
  name: opp-gateway-configmap

namespace: agent-gateway-api

patchesStrategicMerge:
- ingress.yaml
- ingress_controller_patches.yaml
- reloader-patch.yaml
- hpa_patches.yaml