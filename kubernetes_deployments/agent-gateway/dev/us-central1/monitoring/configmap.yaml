apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-adapter
  labels:
    app: prometheus-adapter
data:
  config.yaml: |
    rules:
    - metricsQuery: (avg by (app, kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_sum{kubernetes_namespace="agent-gateway-api", app="agent-gateway-api"}[5m])) / avg by (app,kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_count{kubernetes_namespace="agent-gateway-api", app="agent-gateway-api"}[5m]))) * 1000000
      name:
        as: hydra_server_api_latency_us
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          app:
            resource: deployment
      seriesQuery: hydra_server_semaphore_latency_second_sum{app!="",kubernetes_namespace!=""}

    - metricsQuery: (avg by (app, kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_sum{kubernetes_namespace="agent-gateway-api", app="agent-gateway-api-edr"}[5m])) / avg by (app,kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_count{kubernetes_namespace="agent-gateway-api", app="agent-gateway-api-edr"}[5m]))) * 1000000
      name:
        as: hydra_server_api_edr_latency_us
        matches: ^.*
      resources:
          overrides:
            kubernetes_namespace:
              resource: namespace
            app:
              resource: deployment
      seriesQuery: hydra_server_semaphore_latency_second_sum{app!="",kubernetes_namespace!=""}

    - metricsQuery: (avg by (app, kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_sum{kubernetes_namespace="agent-gateway-api-performance", app="agent-gateway-api"}[5m])) /avg by (app, kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_count{kubernetes_namespace="agent-gateway-api-performance", app="agent-gateway-api"}[5m]))) * 1000000
      name:
        as: hydra_server_api_latency_performance_us
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          app:
            resource: deployment
      seriesQuery: hydra_server_semaphore_latency_second_sum{app!="",kubernetes_namespace!=""}

    - metricsQuery: (avg by (app, kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_sum{kubernetes_namespace="agent-gateway-api-performance", app="agent-gateway-api-edr"}[5m])) / avg by (app,kubernetes_namespace) (rate(hydra_server_semaphore_latency_second_count{kubernetes_namespace="agent-gateway-api-performance", app="agent-gateway-api-edr"}[5m]))) * 1000000
      name:
        as: hydra_server_api_edr_latency_seconds_performance_us
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          app:
            resource: deployment
      seriesQuery: hydra_server_semaphore_latency_second_sum{app!="",kubernetes_namespace!=""}

    - metricsQuery: sum by (kubernetes_pod_name) (hydra_ws_active_conn)
      name:
        as: hydra_ws_active_conn
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_ws_active_conn{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api"}[5m])) / avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api"}[5m]))
      name:
        as: hydra_server_api_latency
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api-edr", kubernetes_namespace="agent-gateway-api"}[5m])) / avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api-edr", kubernetes_namespace="agent-gateway-api"}[5m]))
      name:
        as: hydra_server_api_edr_latency_seconds
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api"}[5m])))
      name:
        as: hydra_server_publicapi_latency
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_pod_name!=""}
    - metricsQuery: avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api-download-pipe", kubernetes_namespace="agent-gateway-api"}[5m])) / avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api-download-pipe", kubernetes_namespace="agent-gateway-api"}[5m]))
      name:
        as: hydra_server_api_download_pipe_latency_seconds
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: sum by (kubernetes_pod_name) (hydra:broker_latency_seconds:avg)
      name:
        as: hydra_server_broker_latency
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra:broker_latency_seconds:avg{kubernetes_pod_name!=""}
    - metricsQuery: avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api-edr", kubernetes_namespace="agent-gateway-api-performance"}[5m])) / avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api-edr", kubernetes_namespace="agent-gateway-api-performance"}[5m]))
      name:
        as: hydra_server_api_edr_latency_seconds_performance_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api-download-pipe", kubernetes_namespace="agent-gateway-api-performance"}[5m])) / avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api-download-pipe", kubernetes_namespace="agent-gateway-api-performance"}[5m]))
      name:
        as: hydra_server_api_download_pipe_latency_seconds_performance_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{kubernetes_namespace!="",kubernetes_pod_name!=""}
    - metricsQuery: ( sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-performance"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-performance"}[5m])))) ) * 1000
      name:
        as: hydra_server_api_latency_performance_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-performance"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-performance"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-performance"}[5m]))))
      name:
        as: hydra_server_publicapi_latency_performance_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-performance"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-performance"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="broker", kubernetes_namespace="agent-gateway-api-performance"}[5m]))))
      name:
        as: hydra_server_broker_latency_performance_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-performance"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-automation"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-automation"}[5m]))))
      name:
        as: hydra_server_api_latency_automation_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-automation"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-automation"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-automation"}[5m]))))
      name:
        as: hydra_server_publicapi_latency_automation_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-automation"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-automation"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="broker", kubernetes_namespace="agent-gateway-api-automation"}[5m]))))
      name:
        as: hydra_server_broker_latency_automation_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-automation"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-test01"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-test01"}[5m]))))
      name:
        as: hydra_server_api_latency_test01_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-api", kubernetes_namespace="agent-gateway-api-test01"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-test01"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-test01"}[5m]))))
      name:
        as: hydra_server_publicapi_latency_test01_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="agent-gateway-public-api", kubernetes_namespace="agent-gateway-api-test01"}
    - metricsQuery: sum by (kubernetes_pod_name) ((avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-test01"}[5m]))) / (avg by (kubernetes_pod_name) (rate(hydra_server_semaphore_latency_second_count{app="broker", kubernetes_namespace="agent-gateway-api-test01"}[5m]))))
      name:
        as: hydra_server_broker_latency_test01_ns
        matches: ^.*
      resources:
        overrides:
          kubernetes_namespace:
            resource: namespace
          kubernetes_pod_name:
            resource: pod
      seriesQuery: hydra_server_semaphore_latency_second_sum{app="broker", kubernetes_namespace="agent-gateway-api-test01"}
