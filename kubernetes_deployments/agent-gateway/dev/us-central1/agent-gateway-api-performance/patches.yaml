apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
  namespace: agent-gateway-api
  labels:
    app: agent-gateway-api
spec:
  minReplicas: 2
  maxReplicas: 40
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api
        metric:
          name: hydra_server_api_latency_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
  namespace: agent-gateway-api-performance
  labels:
    app: agent-gateway-api
spec:
  minReplicas: 10
  maxReplicas: 60
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api
        metric:
          name: hydra_server_api_latency_performance_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-edr
  namespace: agent-gateway-api-performance
  labels:
    app: agent-gateway-api-edr
spec:
  minReplicas: 10
  maxReplicas: 400
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-edr
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api-edr
        metric:
          name: hydra_server_api_edr_latency_seconds_performance_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-edr
  namespace: agent-gateway-api
  labels:
    app: agent-gateway-api-edr
spec:
  minReplicas: 2
  maxReplicas: 40
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-edr
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api-edr
        metric:
          name: hydra_server_api_edr_latency_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-download-pipe
spec:
  maxReplicas: 200
  minReplicas: 5
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-download-pipe
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_api_download_pipe_latency_seconds_performance_ns
        target:
          type: Utilization
          averageValue: "0.000050"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-public-api
spec:
  maxReplicas: 50
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-public-api
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_publicapi_latency_performance_ns
        target:
          type: Utilization
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-ws
spec:
  maxReplicas: 20
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-ws
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-brain
spec:
  maxReplicas: 10
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-brain
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: broker
spec:
  maxReplicas: 10
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: broker
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_broker_latency_performance_ns
        target:
          type: Utilization
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
