apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- namespace.yaml
- ../../../base
- ../../../base/prod_dev_services
- external-apps

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr-qa2-uat.us.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx-qa2-uat.us.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_LOGGER_TRANSPORT=console:-1
  - GONZO_ENV=dev
  - GONZO_PROJECT_ID=xdr-agent-gateway-dev-01
  - GONZO_SLACK_CHANNEL='#hydra-dev-alerts'
  - HYDR<PERSON>_FIRESTORE_PROJECT_ID=xdr-mag-shared-dev-01
  - HYDRA_PROXY_BUDDY=*************
  - HYDRA_PUBSUB_PROJECT_ID=traps-edr-dev-01
  - HYDRA_VISO_HOST=http://**************:6969
  - HYDRA_KMS_PROJECT_ID=xdr-kms-project-dev-01
  - HYDRA_KEY_LOCATION=us-central1
  - HYDRA_KEY_RING=hydra-tenant-ring
  - HYDRA_KEY_VERSION=1
  - HYDRA_WS_MESSAGES_SUBSCRIPTION=hydra-incoming-sub-performance
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-dev-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-dev
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr-qa2-uat.us.paloaltonetworks.com
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32,*************/32,*************/32
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx-qa2-uat.us.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32
  name: agent-gateway-api-configmap-xsoar
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-ws:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-ws:6379
  name: redis-connection-conf-ws
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-edr:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-edr:6379
  name: redis-connection-conf-edr
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-collection:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-collection:6379
  name: redis-connection-conf-collection

namespace: agent-gateway-api-performance

patchesStrategicMerge:
- ingress.yaml
- patches.yaml
- reloader-patch.yaml

images:
- name: agent-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-hf-CRTX-96261-v3.8.0-491-ga87aa
- name: agent-gateway-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: agent-gateway-api-brain
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: agent-gateway-api-download-pipe
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: agent-gateway-api-edr
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: agent-gateway-api-ws
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: agent-gateway-public-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: anubis-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: broker
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: consul-agent
  newName: gcr.io/kuna-224012/consul_agent
  newTag: master-v80
- name: controller
  newName: gcr.io/kuna-224012/3rd-party/ingress-nginx/controller
  newTag: v1.10.1
- name: custom-metrics
  newName: gcr.io/kuna-224012/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: log-collection
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
- name: mysql
  newName: gcr.io/kuna-224012/3rd-party/mysql
  newTag: 8.0.35
- name: nginx
  newName: gcr.io/kuna-224012/3rd-party/nginx
  newTag: 1.26.0-alpine
- name: prometheus
  newName: gcr.io/kuna-224012/3rd-party/prom-tools/prometheus
  newTag: v2.52.0
- name: redis
  newName: gcr.io/kuna-224012/3rd-party/redis
  newTag: 7.2.5-alpine
- name: reloader
  newName: gcr.io/kuna-224012/3rd-party/reloader
  newTag: v1.0.40
- name: stackdriver-exporter
  newName: gcr.io/kuna-224012/3rd-party/stackdriver-exporter
  newTag: v0.15.1
- name: xsoar-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-CRTX-64207-aggregate-analytics-metablob-v3.6.0-251-gac263
