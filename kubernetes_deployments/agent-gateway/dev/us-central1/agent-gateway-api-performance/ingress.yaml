apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-public-api-ingress
spec:
  rules:
  - &public_api_rule
    host: '*.xdr-qa2-uat.us.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-public-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: log-collection
            port:
              number: 8080
        path: /logs/(.*)
        pathType: ImplementationSpecific
  - <<: *public_api_rule
    host: '*.xdr.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    - '*.xdr.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-api-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: agent-gateway-api-performance-address