apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- reloader
- ingress-controller


namePrefix: performance-

patchesJson6902:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: nginx-ingress-controller
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/args/1
        value: "--publish-service=$(POD_NAMESPACE)/performance-nginx-ingress-controller"
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: nginx-ingress-collectors-gateway-controller
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/args/1
        value: "--publish-service=$(POD_NAMESPACE)/performance-nginx-ingress-collectors-gateway-controller"

