apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ***********
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ***************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      containers:
      - args:
        - /nginx-ingress-controller
        - --publish-service=$(POD_NAMESPACE)/performance-nginx-ingress-collectors-gateway-controller
        - --election-id=nginx-ingress-collectors-gateway-leader
        - --controller-class=k8s.io/collector-ingress-nginx
        - --ingress-class=nginx-collector
        - --configmap=$(POD_NAMESPACE)/performance-nginx-ingress-collectors-gateway-controller
        - --watch-namespace=$(POD_NAMESPACE)
        name: controller
        image: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/ingress-nginx/controller:v1.12.1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      containers:
      - args:
        - /nginx-ingress-controller
        - --publish-service=$(POD_NAMESPACE)/performance-nginx-ingress-controller
        - --election-id=nginx-ingress-leader
        - --controller-class=k8s.io/ingress-nginx
        - --ingress-class=nginx
        - --configmap=$(POD_NAMESPACE)/performance-nginx-ingress-controller
        - --watch-namespace=$(POD_NAMESPACE)
        - --enable-metrics=true
        name: controller
        image: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/ingress-nginx/controller:v1.12.1
