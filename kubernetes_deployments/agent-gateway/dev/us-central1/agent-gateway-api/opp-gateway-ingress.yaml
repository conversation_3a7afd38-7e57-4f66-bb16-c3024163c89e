---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/client-max-body-size: 5m
    nginx.ingress.kubernetes.io/proxy-body-size: 5m
    ingress.kubernetes.io/proxy-body-size: 5m
# check ingress config at multi-tenants-projects/kubernetes_deployments/agent-gateway/base/nginx_ingress.yaml
# for more annotations in case we have a bug
    nginx.ingress.kubernetes.io/server-snippet: |-
      location ^~ /metrics {
          return 404;
      }
  name: opp-gateway
spec:
  tls:
    - hosts:
        - downloads.crtx-qa2-uat.us.paloaltonetworks.com
        - onpremgw.crtx-qa2-uat.us.paloaltonetworks.com
      secretName: crtx-qa2-uat.us.paloaltonetworks.com
  rules:
    - host: downloads.crtx-qa2-uat.us.paloaltonetworks.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: opp-gateway
                port:
                  name: http
    - host: onpremgw.crtx-qa2-uat.us.paloaltonetworks.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: opp-gateway
                port:
                  name: http
