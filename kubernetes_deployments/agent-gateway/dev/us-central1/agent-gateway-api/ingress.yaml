---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-public-api-ingress
spec:
  rules:
  - &public_api_rule
    host: '*.xdr-qa2-uat.us.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-public-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: log-collection
            port:
              number: 8080
        path: /logs/(.*)
        pathType: ImplementationSpecific
  - <<: *public_api_rule
    host: '*.crtx-qa2-uat.us.paloaltonetworks.com'
  - http:
      paths:
        - backend:
            service:
              name: log-collection
              port:
                number: 8080
          path: /logs/(.*)
          pathType: ImplementationSpecific
    host: 'agent-gateway-public-api-dev.traps.paloaltonetworks.com'
  tls:
  - hosts:
    - 'agent-gateway-public-api-dev.traps.paloaltonetworks.com'
    secretName: traps.paloaltonetworks.com
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com
  - hosts:
    - '*.crtx-qa2-uat.us.paloaltonetworks.com'
    secretName: crtx-qa2-uat.us.paloaltonetworks.com

---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-buddy-in-ws-dns
subsets:
  - addresses:
      - ip: *************
    ports:
      - port: 80
        protocol: TCP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-grpc-ingress
spec:
  rules:
  - &xsoar_grpc_api_rule
    host: '*.crtx-qa2-uat.us.paloaltonetworks.com'
    http:
      paths:
        - backend:
            service:
              name: proxy-buddy-in-ws-dns
              port:
                number: 80
          path: /xsoar\.(.*)
          pathType: ImplementationSpecific
  - <<: *xsoar_grpc_api_rule
    host: '*.xdr-qa2-uat.us.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com
  - hosts:
    - '*.crtx-qa2-uat.us.paloaltonetworks.com'
    secretName: crtx-qa2-uat.us.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-ingress
spec:
  rules:
  - &xsoar_api_rule
    host: '*.xdr-qa2-uat.us.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/d1ws
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/http-tunnel-over-ws/
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/migration/tunnel/ws
        pathType: ImplementationSpecific
  - <<: *xsoar_api_rule
    host: '*.crtx-qa2-uat.us.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com
  - hosts:
    - '*.crtx-qa2-uat.us.paloaltonetworks.com'
    secretName: crtx-qa2-uat.us.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-api-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: agent-gateway-api-address

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-api-ingress-ipv6
  annotations:
    kubernetes.io/ingress.global-static-ip-name: agent-gateway-api-address-ipv6
