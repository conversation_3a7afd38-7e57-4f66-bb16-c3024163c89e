apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ../../../base
- ../../../base/prod_dev_services
- ../../../../reloader
- ../../../base/nginx-controllers
- ../../../base/deployments/opp-gateway
- ../../../base/deployments/opp-telemetry
- opp-gateway-ingress.yaml

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr-qa2-uat.us.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx-qa2-uat.us.paloaltonetworks.com


generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=dev
  - GONZO_PROJECT_ID=xdr-agent-gateway-dev-01
  - GONZO_SLACK_CHANNEL='#hydra-dev-alerts'
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-public-dev-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-private-dev-01
  - HYDRA_PROXY_BUDDY=*************
  - HYDRA_PUBSUB_PROJECT_ID=traps-edr-dev-01
  - HYDRA_VISO_HOST=http://**************:6969
  - HYDRA_KMS_PROJECT_ID=xdr-kms-project-dev-01
  - HYDRA_KEY_LOCATION=us-central1
  - HYDRA_KEY_RING=hydra-tenant-ring
  - HYDRA_KEY_VERSION=1
  - HYDRA_WS_MESSAGES_SUBSCRIPTION=hydra-incoming-sub
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-dev-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-dev
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr-qa2-uat.us.paloaltonetworks.com
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-dev-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32,*************/32,*************/32
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx-qa2-uat.us.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32
  - HYDRA_PROXY_BUDDY=************* # pb-in-ws-dns
  name: agent-gateway-api-configmap-xsoar
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-ws:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-ws:6379
  name: redis-connection-conf-ws
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-edr:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-edr:6379
  name: redis-connection-conf-edr
- behavior: replace
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-collection:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-collection:6379
  name: redis-connection-conf-collection
- behavior: replace
  literals:
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-dev
  - HYDRA_OPP_OPP_REGISTRY_NAME=xdr-opp-registries-dev-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-dev
  name: opp-gateway-configmap
- behavior: merge
  literals:
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-dev-01
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-dev
  - HYDRA_PUBSUB_PROJECT_ID=xdr-agent-gateway-dev-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-dev
  name: opp-telemetry-configmap

namespace: agent-gateway-api

patchesStrategicMerge:
- ingress.yaml
- hpa_patches.yaml
- ingress_controller_patches.yaml
- reloader-patch.yaml
- deployments_patch.yaml

images:
- name: agent-gateway
  newName: us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway
  newTag: hydra-CRTX-151224-download-pipe-add-api-v3.14-171-gb7659
- name: bucket-subscriber
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/nginx-router-pubsub-subscriber
  newTag: master-v0.0.1
- name: consul-agent
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/consul
  newTag: 1.20.2-v1
- name: controller
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/ingress-nginx/controller
  newTag: v1.11.2
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/custom-metrics-stackdriver-adapter
  newTag: v0.15.1-gke.0
- name: exporter
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/google/cloud-sdk
  newTag: 502.0.0-alpine
- name: k8s-connector-gateway
  newName: us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway
  newTag: hydra-CRTX-154024-add-metrics-route-to-api-v3.15-44-g9bca1
- name: k8s-connector-gateway-api
  newName: us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/agent-gateway
  newTag: hydra-stable-v3.14-414-gbbe82d
- name: minio
  newName: minio/minio
  newTag: RELEASE.2024-03-15T01-07-19Z
- name: mysql
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/mysql
  newTag: 8.0.39-debian
- name: nginx
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/nginx
  newTag: 1.27.1-alpine
- name: openresty
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/openresty-fips
  newTag: ********-alpine-3.20.5-nginx-router
- name: opp-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-better-logs-2-v3.12-723-gd100a2
- name: opp-telemetry
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-better-logs-2-v3.12-723-gd100a2
- name: prometheus
  newName: gcr.io/kuna-224012/3rd-party/prom-tools/prometheus
  newTag: v2.52.0
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: proxy-buddy-in
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/proxy_buddy_in
  newTag: nginx-alpine-v1.27.1
- name: proxy-buddy-out
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/proxy_buddy_out
  newTag: alpine-v3.20.3
- name: redis
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/redis
  newTag: 7.4.0-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/stakater/reloader
  newTag: v1.1.0
- name: sidecar
  newName: gcr.io/kuna-224012/3rd-party/minio/operator
  newTag: v5.0.12
- name: stackdriver-exporter
  newName: gcr.io/kuna-224012/3rd-party/stackdriver-exporter
  newTag: v0.15.1
