apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
spec:
  maxReplicas: 40
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api
        metric:
          name: hydra_server_api_latency_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-edr
spec:
  maxReplicas: 40
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-edr
  metrics:
    - type: Object
      object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: agent-gateway-api-edr
        metric:
          name: hydra_server_api_edr_latency_us
        target:
          type: Value
          value: "10"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: xsoar-gateway
spec:
  maxReplicas: 10
  minReplicas: 6
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: xsoar-gateway
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-download-pipe
spec:
  maxReplicas: 5
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-download-pipe
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_api_download_pipe_latency_seconds
        target:
          type: Utilization
          averageValue: "0.000050"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-ws
spec:
  maxReplicas: 20
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-ws
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: log-collection
spec:
  maxReplicas: 10
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: log-collection
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-heartbeat
spec:
  maxReplicas: 20
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-heartbeat
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-analytics
spec:
  maxReplicas: 20
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-analytics
