apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: **************
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: *************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      containers:
      - name: controller
        image: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/ingress-nginx/controller:v1.12.1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      containers:
      - name: controller
        image: us-docker.pkg.dev/xdr-registry-dev-01/golden-images/ingress-nginx/controller:v1.12.1
