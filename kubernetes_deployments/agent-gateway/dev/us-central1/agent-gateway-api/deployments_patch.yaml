apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-gateway-api-edr
spec:
  template:
    spec:
      containers:
        - name: agent-gateway
          resources:
            limits:
              cpu: "1"
              memory: 6Gi
            requests:
              cpu: 500m
              memory: 4Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-gateway-api-download-pipe
spec:
  template:
    spec:
      containers:
        - name: agent-gateway
          resources:
            limits:
              cpu: "1"
              memory: 6Gi
            requests:
              cpu: 500m
              memory: 4Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: k8s-connector-gateway-api
  namespace: agent-gateway-api
spec:
  replicas: 3