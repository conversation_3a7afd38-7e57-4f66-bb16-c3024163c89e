apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ../../../base
- ../../../base/prod_dev_services
- ../../../../reloader
- ../../../base/nginx-controllers
- ../../../base/deployments/opp-gateway
- ../../../base/deployments/opp-telemetry
- opp-gateway-ingress.yaml

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr.pr.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx.pr.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true
  #Some values here are wrong cause it don't want hydra to send alerts to prod channels
  #TODO Change viso IP and slack channel

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=prod
  - GONZO_PROJECT_ID=xdr-agent-gateway-prod-pr-01
  - GONZO_SLACK_CHANNEL='#hydra-dev-alerts'
  - GONZO_REGION=us-central1
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-pub-prod-us-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-us-01
  - HYDRA_PROXY_BUDDY=************ # pb-in
  - HYDRA_PROXY_BUDDY_MAX_CON=0
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON=200
  - HYDRA_PROXY_BUDDY_TIMEOUT=1800s
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr.pr.paloaltonetworks.com
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-prod-pr-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-prod-pr
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-pr-01
  - HYDRA_PAPI_ALLOWED_IPS=************/32,************/32 # shared-engines FWs
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-prod-pr-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,*************/32,************/32,************/32  # mag-shared FWs & shared-engines FWs
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx.pr.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=************/32,************/32 # shared-engines FWs
  - HYDRA_PROXY_BUDDY=************ # pb-in-ws-dns
  name: agent-gateway-api-configmap-xsoar
- behavior: merge
  literals:
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-pr-01
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-prod-us
  - HYDRA_PUBSUB_PROJECT_ID=xdr-agent-gateway-prod-pr-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-pr
  name: opp-telemetry-configmap
- behavior: merge
  literals:
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-pr
  name: opp-gateway-configmap

namespace: agent-gateway-api

patchesStrategicMerge:
- ingress.yaml
- ingress_controller_patches.yaml
- reloader-patch.yaml
- hpa_patches.yaml

images:
- name: agent-gateway
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.10.0-549-ge0baf
- name: agent-gateway-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-brain
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-download-pipe
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-edr
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-ws
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-public-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: anubis-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-568-g12738
- name: backend
  newName: gcr.io/kuna-224012/kuna-xdr-backend
  newTag: master-v3.9.0-5515-g8416ae
- name: broker
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: controller
  newName: gcr.io/kuna-224012/3rd-party/ingress-nginx/controller
  newTag: v1.10.1
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/google/cloud-sdk
  newTag: 483.0.0-alpine
- name: k8s-connector-gateway
  newName: k8s-connector-gateway-api
- name: log-collection
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: opp-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-opp-v3.9.0-624-g9b44b
- name: opp-telemetry
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-opp-v3.9.0-624-g9b44b
- name: pod-custom-metrics-stackdriver-adapter
  newName: gcr.io/google-containers/custom-metrics-stackdriver-adapter
  newTag: v0.10.1
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: redis
  newName: gcr.io/kuna-224012/3rd-party/redis
  newTag: 7.2.0-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-prod-us-01/prod-images/3rd-party/stakater/reloader
  newTag: v1.0.93
- name: xsoar-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
