---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-buddy-in-ws-dns
subsets:
  - addresses:
      - ip: **************
    ports:
      - port: 80
        protocol: TCP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-grpc-ingress
spec:
  rules:
  - &xsoar_grpc_api_rule
    host: '*.crtx.tw.paloaltonetworks.com'
    http:
      paths:
        - backend:
            service:
              name: proxy-buddy-in-ws-dns
              port:
                number: 80
          path: /xsoar\.(.*)
          pathType: ImplementationSpecific
  - <<: *xsoar_grpc_api_rule
    host: '*.xdr.tw.paloaltonetworks.com'
  tls:
  - secretName: xdr.tw.paloaltonetworks.com
    hosts:
      - '*.xdr.tw.paloaltonetworks.com'
  - secretName: crtx.tw.paloaltonetworks.com
    hosts:
      - '*.crtx.tw.paloaltonetworks.com'

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-public-api-ingress
spec:
  rules:
  - &public_api_rule
    host: '*.xdr.tw.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-public-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: log-collection
            port:
              number: 8080
        path: /logs/(.*)
        pathType: ImplementationSpecific
  - <<: *public_api_rule
    host: '*.crtx.tw.paloaltonetworks.com'
  - http:
      paths:
        - backend:
            service:
              name: log-collection
              port:
                number: 8080
          path: /logs/(.*)
          pathType: ImplementationSpecific
    host: 'agent-gateway-public-api-prod-tw.traps.paloaltonetworks.com'

  tls:
  - secretName: traps.paloaltonetworks.com
    hosts:
      - 'agent-gateway-public-api-prod-tw.traps.paloaltonetworks.com'
  - secretName: xdr.tw.paloaltonetworks.com
    hosts:
      - '*.xdr.tw.paloaltonetworks.com'
  - secretName: crtx.tw.paloaltonetworks.com
    hosts:
      - '*.crtx.tw.paloaltonetworks.com'
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-api-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: agent-gateway-api-address
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anubis-gateway-ingress
spec:
  defaultBackend:
    service:
      name: anubis-gateway
      port:
        number: 8080
  rules:
    - &anubis_rule
      host: "*.xdr.tw.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: anubis-gateway
                port:
                  number: 8080
            path: /Anubis
            pathType: ImplementationSpecific
    - <<: *anubis_rule
      host: '*.crtx.tw.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.tw.paloaltonetworks.com'
    secretName: xdr.tw.paloaltonetworks.com
  - secretName: crtx.tw.paloaltonetworks.com
    hosts:
      - '*.crtx.tw.paloaltonetworks.com'
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: broker-ingress
spec:
  defaultBackend:
    service:
      name: broker
      port:
        number: 8080
  rules:
    - &broker_rule
      host: "*.xdr.tw.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: xsoar-gateway
                port:
                  number: 8080
            path: /api/cas/v1/transporter
            pathType: ImplementationSpecific
          - backend:
              service:
                name: broker
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
    - <<: *broker_rule
      host: '*.crtx.tw.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.tw.paloaltonetworks.com'
    secretName: xdr.tw.paloaltonetworks.com
  - hosts:
    - '*.crtx.tw.paloaltonetworks.com'
    secretName: crtx.tw.paloaltonetworks.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-ingress
spec:
  rules:
  - &xsoar_api_rule
    host: '*.xdr.tw.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/d1ws
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/http-tunnel-over-ws/
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/migration/tunnel/ws
        pathType: ImplementationSpecific
  - <<: *xsoar_api_rule
    host: '*.crtx.tw.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.tw.paloaltonetworks.com'
    secretName: xdr.tw.paloaltonetworks.com
  - hosts:
    - '*.crtx.tw.paloaltonetworks.com'
    secretName: crtx.tw.paloaltonetworks.com
