apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-ws
  labels:
    app: agent-gateway-api-ws
spec:
  maxReplicas: 100
  minReplicas: 50
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-ws
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_ws_active_conn
          selector: {}
        target:
          type: AverageValue
          averageValue: "4k"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
