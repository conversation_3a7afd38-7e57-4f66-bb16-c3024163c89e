apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-public-api
spec:
  maxReplicas: 50
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-public-api
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_publicapi_latency
        target:
          type: AverageValue
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50