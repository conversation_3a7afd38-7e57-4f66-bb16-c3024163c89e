apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../../../../custom-metrics/

namespace: custom-metrics


configMapGenerator:
- name: custom-metrics-params-configmap
  envs:
  - params

vars:
- name: CUSTOM_METRICS_GCP_SA
  objref:
    kind: ConfigMap
    name: custom-metrics-params-configmap
    apiVersion: v1
  fieldref:
    fieldpath: data.CUSTOM_METRICS_GCP_SA

configurations:
  - params.yaml

generatorOptions:
  disableNameSuffixHash: true

patches:
  - service-account-patch.yaml

patchesStrategicMerge:
- patch.yaml