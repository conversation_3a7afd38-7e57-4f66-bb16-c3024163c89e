apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: opp-telemetry
spec:
  maxReplicas: 30
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: opp-telemetry
  metrics:
  - external:
      metric:
        name: pubsub.googleapis.com|subscription|num_undelivered_messages
        selector:
          matchLabels:
            resource.labels.subscription_id: opp-telemetry-sub
      target:
        type: AverageValue
        averageValue: "10"
    type: External