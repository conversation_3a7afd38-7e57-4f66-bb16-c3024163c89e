apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: agent-gateway-api
  name: opp-telemetry
  labels:
    app: opp-telemetry
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: opp-telemetry
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/port: "8899"
        prometheus.io/scrape: "true"
      labels:
        app: opp-telemetry
    spec:
      containers:
        - command:
            - /go/bin/opp_telemetry
          envFrom:
            - configMapRef:
                name: agent-gateway-api-configmap-common
                optional: false
            - configMapRef:
                name: redis-connection-conf-agent-gw
                optional: false
            - configMapRef:
                name: opp-telemetry-configmap
                optional: false
          image: opp-gateway
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /ping/
              port: 8899
              scheme: HTTP
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 1
          name: opp-telemetry
          ports:
            - containerPort: 7032
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ping/
              port: 8899
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 1Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      nodeSelector:
        type: opp-gateway
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: { }
      terminationGracePeriodSeconds: 30