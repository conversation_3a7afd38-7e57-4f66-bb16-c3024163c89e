apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: redis
  name: redis
spec:
  template:
    spec:
      containers:
        - name: redis
          args:
            - --save
            - ""
            - --appendonly
            - "no"
          resources:
            limits:
              cpu: "2"
              memory: 8Gi
            requests:
              cpu: "1"
              memory: 6Gi
      nodeSelector:
        type: $(node_selector)