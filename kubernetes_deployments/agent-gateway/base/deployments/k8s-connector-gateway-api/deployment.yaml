apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: k8s-connector-gateway-api
  name: k8s-connector-gateway-api
  namespace: agent-gateway-api
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: k8s-connector-gateway-api
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/port: "8899"
        prometheus.io/scrape: "true"
      labels:
        app: k8s-connector-gateway-api
    spec:
      containers:
      - command:
        - /go/bin/hydra
        env:
        - name: GONZO_SLACK_TOKEN
          valueFrom:
            secretKeyRef:
              key: gonzo_slack_token.txt
              name: slack-token
        envFrom:
        - configMapRef:
            name: agent-gateway-api-configmap-agent-gw
            optional: false
        - configMapRef:
            name: agent-gateway-api-configmap-common
            optional: false
        - configMapRef:
            name: redis-connection-conf-agent-gw
            optional: false
        image: k8s-connector-gateway
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 2
          httpGet:
            path: /
            port: 7032
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        name: k8s-connector-gateway
        ports:
        - containerPort: 7032
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /ping/
            port: 8899
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 4Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      nodeSelector:
        type: $(node_selector)
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
