apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    reloader.stakater.com/auto: "true"
  name: log-collection
spec:
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/port: "8899"
        prometheus.io/scrape: "true"
    spec:
      containers:
      - name: agent-gateway
        command:
        - /go/bin/http_collector
        env:
          - name: GONZO_SLACK_TOKEN
            valueFrom:
              secretKeyRef:
                key: gonzo_slack_token.txt
                name: slack-token
        envFrom:
        - configMapRef:
            name: agent-gateway-api-configmap-common
            optional: false
        - configMapRef:
            name: redis-connection-conf-collection
            optional: false
        image: agent-gateway
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 2
          httpGet:
            path: /
            port: 7032
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        ports:
        - containerPort: 7032
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /ping/
            port: 8899
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 4Gi
      dnsPolicy: ClusterFirst
      nodeSelector:
        type: $(node_selector)
      restartPolicy: Always
