apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: log-collection
spec:
  maxReplicas: 100
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: log-collection
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 50
