apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    reloader.stakater.com/auto: "true"
  labels:
    app: opp-gateway
  name: opp-gateway
spec:
  selector:
    matchLabels:
      app: opp-gateway
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: opp-gateway
      annotations:
        prometheus.io/port: "8899"
        prometheus.io/scrape: "true"
    spec:
      containers:
      - name: opp-gateway
        command:
        - /go/bin/opp_gateway
        envFrom:
        - configMapRef:
            name: agent-gateway-api-configmap-common
            optional: false
        - configMapRef:
            name: redis-connection-conf-agent-gw
            optional: false
        - configMapRef:
            name: opp-gateway-configmap
            optional: false
        image: opp-gateway
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 2
          httpGet:
            path: /
            port: 7032
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        ports:
        - containerPort: 7032
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /ping/
            port: 8899
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "1"
            memory: 2Gi
          requests:
            cpu: "0.5"
            memory: 1Gi
      dnsPolicy: ClusterFirst
      nodeSelector:
        type: opp-gateway
      restartPolicy: Always
