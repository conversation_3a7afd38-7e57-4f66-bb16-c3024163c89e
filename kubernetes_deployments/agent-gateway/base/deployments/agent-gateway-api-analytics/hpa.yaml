apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-analytics
spec:
  maxReplicas: 100
  minReplicas: 20
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-analytics
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_api_latency
        target:
          type: AverageValue
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70