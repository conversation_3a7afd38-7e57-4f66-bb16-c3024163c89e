apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-download-pipe
spec:
  maxReplicas: 400
  minReplicas: 50
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-download-pipe
  metrics:
    - type: Pods
      pods:
        metric:
          name: api_download_pipe_latency_seconds
        target:
          type: AverageValue
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 60