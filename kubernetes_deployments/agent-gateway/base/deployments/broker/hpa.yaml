apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: broker
spec:
  maxReplicas: 10
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: broker
  metrics:
    - type: Pods
      pods:
        metric:
          name: hydra_server_broker_latency
        target:
          type: AverageValue
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50