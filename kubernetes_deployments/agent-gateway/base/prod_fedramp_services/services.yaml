apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: Internal
  name: agent-gateway-api
  labels:
    app: agent-gateway-api
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  selector:
    app: agent-gateway-api
  type: LoadBalancer

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: Internal
  name: agent-gateway-api-ws
  labels:
    app: agent-gateway-api-ws
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  selector:
    app: agent-gateway-api-ws
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: Internal
  name: agent-gateway-api-edr
  labels:
    app: agent-gateway-api-edr
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  selector:
    app: agent-gateway-api-edr
  type: LoadBalancer