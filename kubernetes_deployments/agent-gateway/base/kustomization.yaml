apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - deployments
  - nginx_ingress.yaml
  - frontend_config.yaml

# Generate certificates
secretGenerator:
- name: traps.paloaltonetworks.com
  files:
  - certs/traps.paloaltonetworks.com/tls.key
  - certs/traps.paloaltonetworks.com/tls.crt
- files:
    - certs/gonzo_slack_token.txt
  name: slack-token

configMapGenerator:
- name: configmap-parameters
  envs:
  - params
- name: agent-gateway-api-configmap-common
  literals:
# Don't forget to override the ${PER_ENVIRONMENT} parameters in every environment kustomization.yaml file (example in dev)
  - GONZO_ENV=${PER_ENVIRONMENT}
  - GONZO_REGION=us-central1
  - GONZO_LOGGER_TRANSPORT=console:1
  - GONZO_METRICS_PORT="8899"
  - GONZO_PROJECT_ID=${PER_ENVIRONMENT}
  - GONZO_SLACK_CHANNEL=${PER_ENVIRONMENT}
  - HYDRA_HYDRA_LOGS_SEVERITY="-1"
  - HYDRA_MAX_HTTP_CLIENT_SEMAPHORE=25s
  - HYDRA_PORT="7032"
  - HYDRA_PROXY_BUDDY=${PER_ENVIRONMENT}
  - HYDRA_PROXY_BUDDY_MAX_CON="0"
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON="200"
  - HYDRA_PROXY_BUDDY_TIMEOUT=2400s
  - HYDRA_PUBSUB_PROJECT_ID=${PER_ENVIRONMENT}
  - HYDRA_VISO_HOST=${PER_ENVIRONMENT}
  - HYDRA_AGENT_ANALYTICS_PROJECT=${PER_ENVIRONMENT}
  - HYDRA_AGENT_ANALYTICS_TOPIC=agent-analytics-topic
  - HYDRA_AGENT_ANALYTICS_BUCKET=${PER_ENVIRONMENT}
  - HYDRA_IP_CAPTURE_ENABLED="false"
  - HYDRA_RATE_LIMIT_REDIS_INSTANCES=redis-svc-limiter-0:6379,redis-svc-limiter-1:6379,redis-svc-limiter-2:6379,redis-svc-limiter-3:6379
  - HYDRA_RATE_LIMIT_DISABLED=false
  - HYDRA_REDUCE_PERIODIC_REPORT_SIZE=true
  - HYDRA_K8S_METRICS_PUBSUB_TOPIC=agent-metrics-topic
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=${PER_ENVIRONMENT}
- name: agent-gateway-api-configmap-edr
  literals:
  - HYDRA_MAX_HTTP_CLIENT_SEMAPHORE=3s
  - HYDRA_MAX_BACKPRESSURE_CLIENTS=50
  - HYDRA_EDR_SKIP_AUTHENTICATION=true
- name: agent-gateway-api-configmap-heartbeat
- name: agent-gateway-api-configmap-analytics
- name: agent-gateway-api-configmap-public-api
  literals:
  - GONZO_LOGGER_TRANSPORT=console:0
  - HYDRA_EDL_NEW_PREFIX=ext
  - HYDRA_AUDIT_BIGQUERY_DATASET_ID=papi_audit
  - HYDRA_AUDIT_BIGQUERY_TABLE_ID=papi_audit
  - HYDRA_AUDIT_IS_AVAILABLE=true
- name: agent-gateway-api-configmap-download-pipe
  literals:
  - HYDRA_PIPE_REDIRECT_ENABLED=true
  - HYDRA_REDIRECT_DEFAULT=false
- name: agent-gateway-api-configmap-agent-gw
  literals:
  - HYDRA_MAX_HTTP_CLIENTS=1000
- name: agent-gateway-api-configmap-agent-gw-ws
  literals:
  - HYDRA_MAX_HTTP_CLIENTS=0
  - HYDRA_MAX_TCP_CLIENTS=0
  - HYDRA_WS=1
  - HYDRA_RATE_LIMIT_SYNC_INTERVAL=15s
- name: agent-gateway-api-configmap-broker
  literals:
  - HYDRA_BROKER_DOCKER_TIMEOUT=720m
  - HYDRA_GCS_DOWNLOAD_REQUEST_TIMEOUT=720m
- name: agent-gateway-api-configmap-xsoar
  literals:
  - HYDRA_EDL_NEW_PREFIX=ext|br
  - HYDRA_WS_ALLOWED_URLS=(/xsoar/(d1ws#http-tunnel-over-ws.*#migration/tunnel/ws)#/api/cas/v1/transporter)
- name: redis-connection-conf-agent-gw
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc:6379
- name: redis-connection-conf-ws
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-ws:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-ws:6379
- name: redis-connection-conf-edr
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-edr:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-edr:6379
- name: redis-connection-conf-collection
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-collection:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-collection:6379
- name: redis-connection-conf-xsoar
  literals:
  - HYDRA_REDIS_CONNECTION_STRING=redis-svc-xsoar:6379
  - GONZO_REDIS_CONNECTION_STRING=redis-svc-xsoar:6379
- name: opp-telemetry-configmap
  # Don't forget to override the ${PER_ENVIRONMENT} parameters in every environment kustomization.yaml file (example in dev)
  literals:
  - HYDRA_OPP_TELEMETRY_SUBSCRIPTION=opp-telemetry-sub
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=${PER_ENVIRONMENT}
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=${PER_ENVIRONMENT}
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=${PER_ENVIRONMENT}
  - HYDRA_PUBSUB_PROJECT_ID=${PER_ENVIRONMENT}
- name: opp-gateway-configmap
  literals:
    - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-prod-us
    - HYDRA_OPP_OPP_REGISTRY_NAME=xdr-opp-registries-prod-us-01
    - HYDRA_OPP_TELEMETRY_BUCKET_NAME=${PER_ENVIRONMENT}
    - HYDRA_OPP_PUB_SUB_PROJECT=xdr-ext-telemetry-prod-us-01

generatorOptions:
  disableNameSuffixHash: true

vars:
- name: node_selector
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.node_selector

configurations:
- params.yaml
