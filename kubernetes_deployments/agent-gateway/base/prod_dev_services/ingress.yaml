apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: gce
    networking.gke.io/v1beta1.FrontendConfig: gke-ssl-policy
  name: agent-gateway-api-ingress
spec:
  defaultBackend:
    service:
      name: agent-gateway-api
      port:
        number: 8080
  rules:
  - http:
      paths:
      - path: /edr/collect
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-edr
            port:
              number: 8080
      - path: /operations/heartbeat
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-heartbeat
            port:
              number: 8080
      - path: /operations/provision/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-heartbeat
            port:
              number: 8080
      - path: /operations/verdicts/get-verdicts
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-heartbeat
            port:
              number: 8080
      - path: /analytics/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-analytics
            port:
              number: 8080
      - path: /download-file/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-download-pipe
            port:
              number: 8080
      - path: /download/file/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-download-pipe
            port:
              number: 8080
      - path: /download-content/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-download-pipe
            port:
              number: 8080
      - path: /content/download-content/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-download-pipe
            port:
              number: 8080
      - path: /operations/socket
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-gateway-api-ws
            port:
              number: 8080
      - path: /k8s/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: k8s-connector-gateway-api
            port:
              number: 8080
  tls:
  - secretName: traps.paloaltonetworks.com
