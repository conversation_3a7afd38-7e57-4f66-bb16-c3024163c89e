apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-agent-gateway-api-ws"}}'
  name: agent-gateway-api-ws
  labels:
    app: agent-gateway-api-ws
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: agent-gateway-api-ws

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-agent-gateway-api"}}'
  name: agent-gateway-api
  labels:
    app: agent-gateway-api
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: agent-gateway-api
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-agent-gateway-api"}}'
  name: agent-gateway-api-edr
  labels:
    app: agent-gateway-api-edr
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: agent-gateway-api-edr
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-agent-gateway-api"}}'
  name: agent-gateway-api-heartbeat
  labels:
    app: agent-gateway-api-heartbeat
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: agent-gateway-api-heartbeat
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-agent-gateway-api"}}'
  name: agent-gateway-api-analytics
  labels:
    app: agent-gateway-api-analytics
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: agent-gateway-api-analytics

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    beta.cloud.google.com/backend-config: '{"ports": {"8080":"backendconfig-k8s-connector-gateway-api"}}'
  name: k8s-connector-gateway-api
  labels:
    app: k8s-connector-gateway-api
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 7032
  type: NodePort
  selector:
    app: k8s-connector-gateway-api

