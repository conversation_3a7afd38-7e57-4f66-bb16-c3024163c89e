---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    ingress.kubernetes.io/proxy-body-size: "25m"
    nginx.ingress.kubernetes.io/client-max-body-size: "25m"
    nginx.ingress.kubernetes.io/proxy-body-size: "25m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "900"
    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
    nginx.ingress.kubernetes.io/send-timeout: "900"
    # this is actually the main server-snippet for all crtx* and xdr* endpoints
    nginx.ingress.kubernetes.io/server-snippet: |-
      location ^~ /metrics {
          return 404;
      }

      # we're not using the built-in auth-url annotation because of the following issue:
      #  - built-in auth-url is setting Host Header to be the auth-url server
      #  - papi auth requires the Host Header to have the value of the original $host
      #  - There's no way to override the built-in Host Header, and attempting to set it again
      #    causes nginx to send two Host headers, which is not valid HTTP request and get dropped
      #    at the papi auth server side.
      
      location = /_papi_auth {
        internal;

        access_log off;

        proxy_pass_request_body     off;
        proxy_set_header            Content-Length          "";
        proxy_set_header            X-Forwarded-Proto       "";
        proxy_set_header            X-Request-ID            $req_id;

        proxy_set_header            Host                    $host;
        proxy_set_header            X-Original-URL          $scheme://$http_host$request_uri;
        proxy_set_header            X-Original-Method       $request_method;
        proxy_set_header            X-Sent-From             "nginx-ingress-controller";
        proxy_set_header            X-Real-IP               $remote_addr;
        proxy_set_header            X-Forwarded-For         $remote_addr;
        proxy_set_header            X-Auth-Request-Redirect $request_uri;

        proxy_buffering                         off;
        proxy_buffer_size                       4k;
        proxy_buffers                           4 4k;
        proxy_request_buffering                 on;

        proxy_ssl_server_name       on;
        proxy_pass_request_headers  on;
        client_max_body_size        0;

        proxy_http_version 1.1;
        set $target "http://agent-gateway-public-api.agent-gateway-api.svc.cluster.local:8080/xsoar/auth/";
        proxy_pass $target;
      }
  name: agent-gateway-public-api-ingress
spec:
  rules:
    - host: "*.xdr.paloaltonetworks.com"
      http:
        paths:
          - backend:
              # This assumes http-svc exists and routes to healthy endpoints.
              service:
                name: agent-gateway-public-api
                port:
                  number: 8080
            path: /(.*)
            pathType: ImplementationSpecific
          - backend:
              service:
                name: log-collection
                port:
                  number: 8080
            path: /logs/(.*)
            pathType: ImplementationSpecific
  defaultBackend:
    service:
      name: agent-gateway-public-api
      port:
        number: 8080
  tls:
    - secretName: xdr.paloaltonetworks.com

---
apiVersion: v1
kind: Service
metadata:
  name: proxy-buddy-in-ws-dns
spec:
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  clusterIP: None  # headless service

---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-buddy-in-ws-dns
subsets:
  - addresses:
      - ip: "${PER_ENVIRONMENT}"  # Don't forget to override the ${PER_ENVIRONMENT} parameters in every environment kustomization.yaml file (example in dev)
    ports:
      - port: 80
        protocol: TCP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    ingress.kubernetes.io/proxy-body-size: "5g"
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/client-max-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      # _papi_auth is configured on agent-gateway-public-api-ingress ingress controller, 
      # which is the main snippet for crtx-* anc xdr-* FQDNs
      auth_request        /_papi_auth;
  name: agent-gateway-xsoar-api-grpc-ingress
spec:
  rules:
    - host: "*.crtx.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: proxy-buddy-in-ws-dns
                port:
                  number: 80
            path: /xsoar\.(.*)
            pathType: ImplementationSpecific
  tls:
    - secretName: crtx.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/client-max-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "86400"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "86400"
    nginx.ingress.kubernetes.io/send-timeout: "900"
  name: agent-gateway-xsoar-api-ingress
spec:
  rules:
    - host: "*.xdr.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: xsoar-gateway
                port:
                  number: 8080
            path: /xsoar/(.*)
            pathType: ImplementationSpecific
  tls:
    - secretName: xdr.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx-collector
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    ingress.kubernetes.io/proxy-body-size: "5g"
    nginx.ingress.kubernetes.io/client-max-body-size: "5g"
    nginx.ingress.kubernetes.io/proxy-body-size: "5g"
  name: broker-ingress
spec:
  defaultBackend:
    service:
      name: broker
      port:
        number: 8080
  rules:
    - &broker_rule
      host: "*.xdr-qa2-uat.us.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: xsoar-gateway
                port:
                  number: 8080
            path: /api/cas/v1/transporter
            pathType: ImplementationSpecific
          - backend:
              service:
                name: broker
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
    - <<: *broker_rule
      host: "*.crtx-qa2-uat.us.paloaltonetworks.com"
  tls:
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com
  - hosts:
    - '*.crtx-qa2-uat.us.paloaltonetworks.com'
    secretName: crtx-qa2-uat.us.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx-collector
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    ingress.kubernetes.io/proxy-body-size: "5g"
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/server-snippet: "grpc_read_timeout 86400s; grpc_send_timeout 86400s; client_body_timeout 86400s;"
    nginx.ingress.kubernetes.io/client-max-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
  name: anubis-gateway-ingress
spec:
  defaultBackend:
    service:
      name: anubis-gateway
      port:
        number: 8080
  rules:
    - &anubis_rule
      host: "*.xdr-qa2-uat.us.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: anubis-gateway
                port:
                  number: 8080
            path: /Anubis
            pathType: ImplementationSpecific
    - <<: *anubis_rule
      host: '*.crtx-qa2-uat.us.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr-qa2-uat.us.paloaltonetworks.com'
    secretName: xdr-qa2-uat.us.paloaltonetworks.com
  - hosts:
    - '*.crtx-qa2-uat.us.paloaltonetworks.com'
    secretName: crtx-qa2-uat.us.paloaltonetworks.com
