apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ../../../base
- ../../../base/prod_dev_services
- ../../../../reloader
- ../../../base/nginx-controllers
- ../../../base/deployments/opp-gateway
- ../../../base/deployments/opp-telemetry
- opp-gateway-ingress.yaml

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr.ch.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx.ch.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true
  #Some values here are wrong cause it don't want hydra to send alerts to prod channels
  #TODO Change viso IP and slack channel

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=prod
  - GONZO_PROJECT_ID=xdr-agent-gateway-prod-ch-01
  - GONZO_SLACK_CHANNEL='#hydra-dev-alerts'
  - GONZO_REGION=europe-west6
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-pub-prod-us-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-us-01
  - HYDRA_PROXY_BUDDY=**********
  - HYDRA_VISO_HOST=http://************:6969
  - HYDRA_PROXY_BUDDY_MAX_CON=0
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON=200
  - HYDRA_PROXY_BUDDY_TIMEOUT=1800s
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr.ch.paloaltonetworks.com
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-prod-ch-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-prod-ch
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-ch-01
  - HYDRA_PAPI_ALLOWED_IPS=************/32,************/32
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-prod-ch-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32,************/32,************/32
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx.ch.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PROXY_BUDDY=*********** # pb-in-ws-dns
  - HYDRA_PAPI_ALLOWED_IPS=************/32,************/32
  name: agent-gateway-api-configmap-xsoar
- behavior: merge
  literals:
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-ch-01
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-prod-us
  - HYDRA_PUBSUB_PROJECT_ID=xdr-agent-gateway-prod-ch-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-ch
  name: opp-telemetry-configmap
- behavior: merge
  literals:
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-ch
  name: opp-gateway-configmap

namespace: agent-gateway-api

patchesStrategicMerge:
- ingress.yaml
- ingress_controller_patches.yaml
- reloader-patch.yaml
- hpa_patches.yaml

images:
- name: agent-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-stable-v3.6.0-378-g3facd4
- name: agent-gateway-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-brain
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-download-pipe
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-edr
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-ws
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-public-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: anubis-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-568-g12738
- name: broker
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: controller
  newName: gcr.io/kuna-224012/ingress-nginx/controller
  newTag: v1.3.1
- name: log-collection
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: redis
  newName: gcr.io/kuna-224012/redis
- name: reloader
  newName: gcr.io/kuna-224012/stakater/reloader
  newTag: v0.0.49
- name: xsoar-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
