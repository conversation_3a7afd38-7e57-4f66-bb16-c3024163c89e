apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
spec:
  minReplicas: 150
  maxReplicas: 300
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-ws
spec:
  minReplicas: 40
  maxReplicas: 200
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-ws
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-edr
spec:
  minReplicas: 100
  maxReplicas: 400
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api-edr
  metrics:
    - type: Pods
      pods:
        metric:
          name: api_edr_latency_seconds
        target:
          type: AverageValue
          averageValue: "0.000015"
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 60
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: xsoar-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: xsoar-gateway
  maxReplicas: 24
  minReplicas: 24
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-heartbeat
spec:
  minReplicas: 250
  maxReplicas: 1000
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-analytics
spec:
  minReplicas: 150
  maxReplicas: 1000