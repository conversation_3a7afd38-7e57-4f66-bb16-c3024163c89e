apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ../../../base
- ../../../../reloader
- ../../../base/prod_dev_services
- ../../../base/nginx-controllers
- ../../../base/deployments/opp-gateway
- ../../../base/deployments/opp-telemetry
- opp-gateway-ingress.yaml

# Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr.us.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx.us.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=prod
  - GONZO_PROJECT_ID=xdr-agent-gateway-prod-us-01
  - GONZO_SLACK_CHANNEL='#hydra-prod-alerts'
  - GONZO_REGION=us-central1
  - HYDRA_FIRESTORE_PROJECT_ID=xdr-mag-shared-prod-us-01
  - HYDRA_PROXY_BUDDY=************
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-pub-prod-us-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-us-01
  - HYDRA_PUBSUB_PROJECT_ID=traps-edr-prod-us-01
  - HYDRA_VISO_HOST=http://*************:6969
  - HYDRA_PROXY_BUDDY_MAX_CON=0
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON=200
  - HYDRA_SLACK_INTEGRATION_URL=slck.xdr.us.paloaltonetworks.com
  - HYDRA_KMS_PROJECT_ID=xdr-kms-project-prod-us-01
  - HYDRA_KEY_LOCATION=us-central1
  - HYDRA_KEY_RING=hydra-tenant-ring
  - HYDRA_KEY_VERSION=1
  - HYDRA_WS_MESSAGES_SUBSCRIPTION=hydra-incoming-sub
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-prod-us-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-prod-us
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-us-01
  - HYDRA_PAPI_ALLOWED_IPS=**************/32,************/32
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-prod-us-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=************/32,************/32,**************/32,************/32
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx.us.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=**************/32,************/32
  - HYDRA_PROXY_BUDDY=*********** # pb-in-ws-dns
  name: agent-gateway-api-configmap-xsoar
- behavior: merge
  literals:
  - HYDRA_RATE_LIMIT_LRU_SIZE=400
  - HYDRA_RATE_LIMIT_STEPS_COUNTER=2000
  - HYDRA_RATE_LIMIT_SYNC_INTERVAL=15s
  name: agent-gateway-api-configmap-agent-gw
- behavior: merge
  literals:
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-us
  name: opp-gateway-configmap
- behavior: merge
  literals:
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-us-01
  - HYDRA_OPP_XSOAR_MARKETPLACE_BUCKET_NAME=marketplace-xsoar-prod-us
  - HYDRA_PUBSUB_PROJECT_ID=xdr-agent-gateway-prod-us-01
  - HYDRA_OPP_TELEMETRY_BUCKET_NAME=opp-telemetry-prod-us
  name: opp-telemetry-configmap

namespace: agent-gateway-api
patchesStrategicMerge:
- ingress.yaml
- hpa_patches.yaml
- ingress_controller_patches.yaml
- deployment_patches.yaml
- reloader-patch.yaml
- redis-ws-patch.yaml
images:
- name: agent-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.9.0-548-g831a8
- name: agent-gateway-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-brain
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-download-pipe
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-edr
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-api-ws
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: agent-gateway-public-api
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: anubis-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-568-g12738
- name: broker
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: controller
  newName: gcr.io/kuna-224012/3rd-party/ingress-nginx/controller
  newTag: v1.10.1
- name: log-collection
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
- name: opp-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-print-auth-input-v3.8.0-584-gf61ad
- name: opp-telemetry
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-opp-v3.9.0-623-g2d574
- name: redis
  newName: gcr.io/kuna-224012/3rd-party/redis
  newTag: 7.2.0-alpine
- name: reloader
  newName: gcr.io/kuna-224012/3rd-party/reloader
  newTag: v1.0.40
- name: xsoar-gateway
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.5-542-gd6bcc2
