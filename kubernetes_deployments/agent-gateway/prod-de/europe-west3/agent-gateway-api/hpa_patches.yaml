apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: xsoar-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: xsoar-gateway
  maxReplicas: 32
  minReplicas: 32
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-heartbeat
spec:
  minReplicas: 250
  maxReplicas: 1000
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-analytics
spec:
  minReplicas: 150
  maxReplicas: 1000
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
spec:
  minReplicas: 600
  maxReplicas: 1000
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-public-api
spec:
  minReplicas: 10
  maxReplicas: 10
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-public-api
