apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: **************
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: *************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      containers:
      - name: controller
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 4Gi
      nodeSelector:
        type: prod-in
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  template:
    spec:
      nodeSelector:
        type: prod-in
