apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api
spec:
  minReplicas: 150
  maxReplicas: 800
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-gateway-api
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-heartbeat
spec:
  minReplicas: 250
  maxReplicas: 1000
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-gateway-api-analytics
spec:
  minReplicas: 150
  maxReplicas: 1000