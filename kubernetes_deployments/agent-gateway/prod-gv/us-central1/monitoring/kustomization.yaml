apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base/monitoring

namespace: monitoring
images:
- name: agent-gateway
  newName: us.gcr.io/xdr-registry-prod-gv-01/agent-gateway
  newTag: hydra-master-fips-gv-v3.11-288-g14b26
- name: consul-agent
  newName: us.gcr.io/xdr-registry-prod-gv-01/consul_agent
  newTag: master-v80
- name: custom-metrics
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: mysql
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/mysql
  newTag: 8.0.35
- name: prometheus
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/prom-tools/prometheus
  newTag: v2.51.1
- name: prometheus-adapter
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/prom-tools/k8s-prometheus-adapter
  newTag: v1.10.0
- name: reloader
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/stakater/reloader
  newTag: v1.0.93
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
- name: stackdriver-exporter-7h
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
