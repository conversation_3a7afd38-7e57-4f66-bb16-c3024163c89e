---
apiVersion: v1
kind: Endpoints
metadata:
  name: proxy-buddy-in-ws-dns
subsets:
  - addresses:
      - ip: **************
    ports:
      - port: 80
        protocol: TCP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-grpc-ingress
spec:
  rules:
  - &xsoar_grpc_api_rule
    host: '*.crtx.gv.paloaltonetworks.com'
    http:
      paths:
        - backend:
            service:
              name: proxy-buddy-in-ws-dns
              port:
                number: 80
          path: /xsoar\.(.*)
          pathType: ImplementationSpecific
  - <<: *xsoar_grpc_api_rule
    host: '*.xdr.gv.paloaltonetworks.com'
  tls:
  - secretName: xdr.gv.paloaltonetworks.com
    hosts:
      - '*.xdr.gv.paloaltonetworks.com'
  - secretName: crtx.gv.paloaltonetworks.com
    hosts:
      - '*.crtx.gv.paloaltonetworks.com'

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-public-api-ingress
spec:
  rules:
  - &public_api_rule
    host: '*.xdr.gv.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-public-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: log-collection
            port:
              number: 8080
        path: /logs/(.*)
        pathType: ImplementationSpecific
  - <<: *public_api_rule
    host: '*.crtx.gv.paloaltonetworks.com'
  - host: '*.traps.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: agent-gateway-api-edr
            port:
              number: 8080
        path: /(edr/collect)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: agent-gateway-api-ws
            port:
              number: 8080
        path: /(operations/socket)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
  - hosts:
    - '*.traps.paloaltonetworks.com'
    secretName: traps.paloaltonetworks.com

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anubis-gateway-ingress
spec:
  defaultBackend:
    service:
      name: anubis-gateway
      port:
        number: 8080
  rules:
    - &anubis_rule
      host: "*.xdr.gv.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: anubis-gateway
                port:
                  number: 8080
            path: /Anubis
            pathType: ImplementationSpecific
    - <<: *anubis_rule
      host: '*.crtx.gv.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: broker-ingress
spec:
  defaultBackend:
    service:
      name: broker
      port:
        number: 8080
  rules:
    - &broker_rule
      host: "*.xdr.gv.paloaltonetworks.com"
      http:
        paths:
          - backend:
              service:
                name: xsoar-gateway
                port:
                  number: 8080
            path: /api/cas/v1/transporter
            pathType: ImplementationSpecific
          - backend:
              service:
                name: broker
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
    - <<: *broker_rule
      host: '*.crtx.gv.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-ingress
spec:
  rules:
  - &xsoar_api_rule
    host: '*.xdr.gv.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/d1ws
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/http-tunnel-over-ws/
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/migration/tunnel/ws
        pathType: ImplementationSpecific
  - <<: *xsoar_api_rule
    host: '*.crtx.gv.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
