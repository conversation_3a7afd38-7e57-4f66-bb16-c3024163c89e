apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-public-api-ingress-proxy
  annotations:
    kubernetes.io/ingress.class: nginx-proxy
    nginx.ingress.kubernetes.io/use-proxy-protocol: "true"
    kubernetes.io/ingress.allow-http: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    ingress.kubernetes.io/proxy-body-size: "25m"
    nginx.ingress.kubernetes.io/client-max-body-size: "25m"
    nginx.ingress.kubernetes.io/proxy-body-size: "25m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "900"
    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
    nginx.ingress.kubernetes.io/send-timeout: "900"
    nginx.ingress.kubernetes.io/server-snippet: |-
      location ^~ /metrics {
          return 404;
      }
spec:
  rules:
  - &public_api_rule
    host: '*.xdr.gv.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-public-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: log-collection
            port:
              number: 8080
        path: /logs/(.*)
        pathType: ImplementationSpecific
  - <<: *public_api_rule
    host: '*.crtx.gv.paloaltonetworks.com'
  - host: '*.traps.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: agent-gateway-api
            port:
              number: 8080
        path: /(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: agent-gateway-api-edr
            port:
              number: 8080
        path: /(edr/collect)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: agent-gateway-api-ws
            port:
              number: 8080
        path: /(operations/socket)
        pathType: ImplementationSpecific
  - http:
      paths:
        - backend:
            service:
              name: log-collection
              port:
                number: 8080
          path: /logs/(.*)
          pathType: ImplementationSpecific
    host: 'agent-gateway-public-api-prod-gv.traps.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
  - hosts:
    - '*.traps.paloaltonetworks.com'
    secretName: traps.paloaltonetworks.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-gateway-xsoar-api-ingress-proxy
  annotations:
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.class: nginx-proxy
    nginx.ingress.kubernetes.io/use-proxy-protocol: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/client-max-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "900"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "86400"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "86400"
    nginx.ingress.kubernetes.io/send-timeout: "900"
    nginx.ingress.kubernetes.io/server-snippet: |-
      location ^~ /metrics {
          return 404;
      }
spec:
  rules:
  - &xsoar_api_rule
    host: '*.xdr.gv.paloaltonetworks.com'
    http:
      paths:
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/d1ws
        pathType: ImplementationSpecific
      - backend:
          service:
            name: xsoar-gateway
            port:
              number: 8080
        path: /xsoar/http-tunnel-over-ws/
        pathType: ImplementationSpecific
  - <<: *xsoar_api_rule
    host: '*.crtx.gv.paloaltonetworks.com'
  tls:
  - hosts:
    - '*.xdr.gv.paloaltonetworks.com'
    secretName: xdr.gv.paloaltonetworks.com
  - hosts:
    - '*.crtx.gv.paloaltonetworks.com'
    secretName: crtx.gv.paloaltonetworks.com
