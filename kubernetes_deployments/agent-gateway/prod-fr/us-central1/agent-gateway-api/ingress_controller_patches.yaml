apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ************
  #loadBalancerIP: ***************

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ************
  #loadBalancerIP: ************
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
  name: nginx-ingress-proxy-gateway-controller
  namespace: agent-gateway-api
spec:
  loadBalancerIP: ************
  #loadBalancerIP: ************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  replicas: 10
  template:
    spec:
      containers:
      - name: controller
        image: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/ingress-nginx/controller:v1.12.3
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 4Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  replicas: 10
  template:
    spec:
      containers:
      - name: controller
        image: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/ingress-nginx/controller:v1.12.3
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nginx-ingress-collectors-gateway-controller
  namespace: agent-gateway-api
spec:
  maxReplicas: 50
  minReplicas: 10
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nginx-ingress-controller
  namespace: agent-gateway-api
spec:
  maxReplicas: 20
  minReplicas: 10
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nginx-ingress-controller
