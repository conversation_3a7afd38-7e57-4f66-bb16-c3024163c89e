fullnameOverride: nginx-ingress-proxy-gateway
nameOverride: nginx-ingress-proxy-gateway-controller

controller:
  nodeSelector:
    type: $(node_selector)
  annotations:
    configmap.reloader.stakater.com/reload: nginx-ingress-proxy-gateway-controller
  ingressClass: nginx-proxy
  ingressClassResource:
    default: false
    # Ingress Class can't work when the Ingress Controller is namespaced
    enabled: false
    # We still need this name and value
    name: nginx-proxy
    controllerValue: "k8s.io/proxy-ingress-nginx"
  admissionWebhooks:
    enabled: False
  scope:
    enabled: true
  image:
    digest: false
    registry: us-docker.pkg.dev/xdr-registry-prod-fr-01
    image: golden-images/ingress-nginx/controller
    tag: v1.12.3
  podAnnotations:
    prometheus.io/port: "10254"
    prometheus.io/scrape: "true"
  service:
    externalTrafficPolicy: Cluster
    enableHttp: false
    ipFamilies: {}
    ipFamilyPolicy: {}
  config:
    ssl-protocols: "TLSv1.2 TLSv1.3"
    ssl-ciphers: "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256"
    large-client-header-buffers: "4 64k"
    client-body-buffer-size: "10m"
    max-worker-connections: "200000"
    annotations-risk-level: "Critical" # For server-snippet https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/annotations-risk/
  allowSnippetAnnotations: true  # Required to use snippets as of 1.12 https://github.com/kubernetes/ingress-nginx/pull/11819
  autoscaling:
    enabled: true
    maxReplicas: 10
    minReplicas: 1
    targetCPUUtilizationPercentage: 50
    targetMemoryUtilizationPercentage: null
  resources:
    limits:
      cpu: "2"
      memory: 4Gi
    requests:
      cpu: "1"
      memory: 4Gi

