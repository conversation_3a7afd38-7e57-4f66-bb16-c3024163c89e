apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- ingress_proxy_gateway.yaml
- ../../../base
- ../../../../reloader
- ../../../base/prod_fedramp_services
- ../../../base/nginx-controllers

helmCharts:
- name: ingress-nginx
  namespace: agent-gateway-api
  releaseName: ingress-nginx-proxy-gateway
  repo: https://kubernetes.github.io/ingress-nginx/
  valuesFile: nginx-ingress-proxy-values.yaml
  version: 4.8.3

  # Generate certificates
secretGenerator:
- files:
  - certs/xdr/tls.key
  - certs/xdr/tls.crt
  name: xdr.federal.paloaltonetworks.com
- files:
  - certs/crtx/tls.key
  - certs/crtx/tls.crt
  name: crtx.federal.paloaltonetworks.com

generatorOptions:
  disableNameSuffixHash: true
  #- HYDRA_VISO_HOST=http://*************:6969

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_ENV=prod
  - GONZO_PROJECT_ID=xdr-agent-gateway-prod-fr-01
  - GONZO_SLACK_CHANNEL=''
  - GONZO_REGION=us-central1
  - HYDRA_FIRESTORE_PROJECT_ID=xdr-mag-shared-prod-fr-01
  - HYDRA_PROXY_BUDDY=************
  - HYDRA_PUBLIC_FIRESTORE_PROJECT_ID=xdr-firestore-pub-prod-fr-01
  - HYDRA_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-fr-01
  - HYDRA_PUBSUB_PROJECT_ID=traps-edr-prod-fr-01
  - HYDRA_PROXY_BUDDY_MAX_CON=0
  - HYDRA_PROXY_BUDDY_MAX_IDLE_CON=200
  - HYDRA_PROXY_BUDDY_TIMEOUT=5m
  - HYDRA_KMS_PROJECT_ID=xdr-kms-project-prod-fr-01
  - HYDRA_KEY_LOCATION=us-central1
  - HYDRA_KEY_RING=hydra-tenant-ring
  - HYDRA_KEY_VERSION=1
  - HYDRA_WS_MESSAGES_SUBSCRIPTION=hydra-incoming-sub
  - HYDRA_SLACK_INTEGRATION_URL=slck-prod-fed.xdr.federal.paloaltonetworks.com
  - HYDRA_AGENT_ANALYTICS_PROJECT=xdr-agent-analytics-prod-fr-01
  - HYDRA_AGENT_ANALYTICS_BUCKET=agent-analytics-uploads-prod-fr
  - HYDRA_AUDIT_BIGQUERY_PROJECT_ID=xdr-bq-mt-stats-prod-fr-01
  - HYDRA_PAPI_ALLOWED_IPS=************/32,**************/32
  - HYDRA_K8S_METRICS_PUBSUB_PROJECT_ID=xdr-ext-telemetry-prod-fr-01
  name: agent-gateway-api-configmap-common
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=*************/32,************/32,**************/32
  - HYDRA_CAS_WEBHOOK_INTEGRATION_URL=integration.crtx.federal.paloaltonetworks.com
  name: agent-gateway-api-configmap-public-api
- behavior: merge
  literals:
  - HYDRA_PAPI_ALLOWED_IPS=************/32,**************/32
  - HYDRA_PROXY_BUDDY=************
  name: agent-gateway-api-configmap-xsoar

namespace: agent-gateway-api

patchesStrategicMerge:
- ingress.yaml
- ingress_controller_patches.yaml
- anubis_gateway_patches.yaml
- reloader-patch.yaml
#- ingress_proxy_gateway.yaml

images:
- name: agent-gateway
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/cortex-xdr/agent-gateway
  newTag: hydra-master-fips-fips-fr-v3.14-471-gfd9fbb
- name: bucket-subscriber
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/nginx-router-pubsub-subscriber
  newTag: v1-2502111834
- name: consul-agent
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/consul
  newTag: 1.20.2-v1
- name: controller
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/ingress-nginx/controller
  newTag: v1.12.0
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/custom-metrics-stackdriver-adapter
  newTag: v0.15.1-gke.0
- name: exporter
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/google/cloud-sdk
  newTag: 524.0.0-alpine
- name: k8s-connector-gateway
  newName: k8s-connector-gateway-api
- name: mysql
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/mysql
  newTag: 8.0.41-debian
- name: nginx
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/nginx
  newTag: 1.28.0-alpine
- name: openresty
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/openresty-fips
  newTag: ********-alpine-3.20.5-nginx-router
- name: prometheus
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/prometheus
  newTag: v2.47.2
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: proxy-buddy-in
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/proxy_buddy_in
  newTag: nginx-alpine-v1.27.1
- name: proxy-buddy-out
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/proxy_buddy_out
  newTag: alpine-v3.20.3
- name: redis
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/redis
  newTag: 7.4.0-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/stakater/reloader
  newTag: v1.1.0
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
