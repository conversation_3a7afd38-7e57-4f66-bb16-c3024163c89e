apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - namespace.yaml
  - ../../../base-hydra
  - ../../../../reloader

patchesStrategicMerge:
  - reloader-patch.yaml

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
  - behavior: merge
    envs:
      - params
    name: configmap-parameters
  - behavior: merge
    literals:
      - GONZO_REGION=europe-west8
      - APP_PROXY_PROXY_BUDDY=192.168.32.3 # pb-in-ui
      - APP_PROXY_SESSION_COOKIE_NAME=app-proxy-hydra-prod-it
      - APP_PROXY_SESSION_REF_COOKIE_NAME=app-proxy-hydra-prod-it-ref
      - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://app-proxy.it.paloaltonetworks.com
      - APP_PROXY_REGIONAL_DOMAIN=it.paloaltonetworks.com
      - APP_PROXY_PRIVATE_SPANNER_DATABASE_ID=spanner_otp_prod-it
    name: appproxy-go-config
  - behavior: merge
    literals:
      - APP_PROXY_AUDIT_BIGQUERY_PROJECT_ID=xdr-app-proxy-prod-it-01
    name: appproxy-go-config
namespace: app-proxy-blue


secretGenerator:
  - files:
      - certs/jwt_cert.pem
      - certs/jwt_key.pem
      - certs/proxy.key
      - certs/proxy.paloaltonetworks.com.pem
      - certs/xdr/tls.crt
      - certs/xdr/tls.key
      - certs/idp_okta.pem
    name: appproxy-certs
  - files:
      - certs/crtx/tls.crt
      - certs/crtx/tls.key
    name: appproxy-crtx-certs
  - literals:
      - cookiesecret.key=bcC3i9kZ54xaG0McQbHC1MZs5zY0HhlCk8bgLpQUKHIjqubgTk7ONjJRrZ3KSiT3
    name: appproxy-cookiesecret
