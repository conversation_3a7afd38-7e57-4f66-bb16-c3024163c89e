apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: nginx-router
resources:
- ../../../nginx-router
- ../../../nginx-router/openresty-v2
- namespace.yaml

patchesStrategicMerge:
- custom-service.yaml
- custom-service-v2.yaml
- subscriber-patch.yaml
- open-resty-patch.yaml

generatorOptions:
  disableNameSuffixHash: true


configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GREEN_TARGET=app-proxy-svc.app-proxy-green.svc.cluster.local:7032
  - DEFAULT_TARGET=app-proxy-svc.app-proxy-blue.svc.cluster.local:7032
  - DEV_TARGET=app-proxy-svc.app-proxy-hydra-playground.svc.cluster.local:7032
  - PLAYGROUND_TARGET=app-proxy-svc.app-proxy-hydra-playground.svc.cluster.local:7032
  - QA_TARGET=app-proxy-svc.app-proxy-hydra-qa.svc.cluster.local:7032
  name: targets
- behavior: merge
  files:
  - conf_overrides/nginx.conf
  - conf_overrides/upstreams.nginx.conf
  name: nginx-conf
- behavior: merge
  files:
  - conf_overrides/nginx-v2.conf
  name: nginx-conf-v2


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
images:
- name: bucket-subscriber
  newName: gcr.io/kuna-224012/nginx-router-pubsub-subscriber
  newTag: test
- name: consul-agent
  newName: gcr.io/kuna-224012/consul_agent
  newTag: rm_kubectl-v78
- name: consul-template
  newName: gcr.io/kuna-224012/consul_agent
  newTag: rm_kubectl-v78
- name: custom-metrics
  newName: gcr.io/kuna-224012/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: exporter
  newName: gcr.io/kuna-224012/3rd-party/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: openresty
  newName: gcr.io/kuna-224012/openresty-fips
  newTag: python-apk-pip-stream



vars:
- fieldref:
    fieldPath: data.QA_TARGET
  name: qa_target
  objref:
    apiVersion: v1
    kind: ConfigMap
    name: targets
- fieldref:
    fieldPath: data.PLAYGROUND_TARGET
  name: playground_target
  objref:
    apiVersion: v1
    kind: ConfigMap
    name: targets
- fieldref:
    fieldPath: data.DEV_TARGET
  name: dev_target
  objref:
    apiVersion: v1
    kind: ConfigMap
    name: targets
