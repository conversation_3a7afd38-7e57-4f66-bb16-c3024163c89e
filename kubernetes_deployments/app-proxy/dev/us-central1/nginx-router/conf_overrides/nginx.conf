worker_processes auto;

error_log /dev/stdout error;
pid       /tmp/nginx.pid;

worker_rlimit_nofile 100000;
events {
  worker_connections 100000;
}

# declare these environment variable to pass on to lua
env DEFAULT_TARGET;
env GREEN_TARGET;
env BLUE_TARGET;
env QA_TARGET;
env DEV_TARGET;
env PLAYGROUND_TARGET;

http {
  log_format json_combined escape=json
  '{'
  '"time":"$msec",'
  '"httpRequest":{'
      '"requestMethod":"$request_method",'
      '"requestUrl":"$scheme://$host$request_uri",'
      '"requestSize":$request_length,'
      '"status":"$status",'
      '"responseSize":$bytes_sent,'
      '"userAgent":"$http_user_agent",'
      '"remoteIp":"$proxy_protocol_addr - $remote_addr",'
      '"serverIp":"$server_addr",'
      '"referer":"$http_referer",'
      '"latency":"${request_time}s",'
      '"protocol":"$server_protocol",'
      '"mth":"$cookie_mth-token",'
      '"remote_port":"$remote_port"'
      '}'
  '}';

  log_format exporter '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$host" "$upstream_response_time"';


  access_log /dev/stdout json_combined;
  access_log syslog:server=127.0.0.1:5531,tag=nginx,severity=info exporter;
  error_log syslog:server=127.0.0.1:5532,tag=nginxerror,severity=info;

  client_body_buffer_size 100M;

  sendfile            on;
  tcp_nopush          on;
  tcp_nodelay         on;
  keepalive_timeout   65;
  types_hash_max_size 2048;
  include             /usr/local/openresty/nginx/conf/mime.types;
  default_type        application/octet-stream;

  proxy_send_timeout 900;
  proxy_read_timeout 900;

  large_client_header_buffers 4 512k;

  #Required to handle larger number of vhosts
  server_names_hash_bucket_size 512;

  #Hide nginx version information
  server_tokens off;

  map $http_upgrade $connection_upgrade {
      default upgrade;
      '' close;
  }

  resolver kube-dns.kube-system.svc.cluster.local valid=10s;

  # default app-proxy endpoint
  upstream frontends {
    server $(default_target);
  }



  #gzip  on;

  # Load modular configuration files from the /etc/nginx/conf.d directory.
  # See http://nginx.org/en/docs/ngx_core_module.html#include
  # for more information.
  include /etc/nginx/conf.d/*.conf;

    server {
        listen 8999 default_server;
        location /ping {
            access_log off;
            return 200 "pong\n";
        }
    }
    server {
        listen      8090;
        server_name localhost;

        #Added for prometheus-nginx-exporter
        location /metrics {
          stub_status;
          allow 127.0.0.1;    #only allow requests from localhost
          deny all;           #deny all other hosts
        }
    }
    # default app-proxy with lua
    server {
        listen       8443  http2  ssl proxy_protocol;
        server_name $(servername_base).$(region).paloaltonetworks.com;

        ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:!3DES;
        ssl_prefer_server_ciphers off;
        ssl_certificate /etc/nginx/ssl/$(base_ssl_cert);
        ssl_certificate_key /etc/nginx/ssl/$(base_ssl_key);

        location / {
            client_max_body_size    4096m;
            proxy_pass_request_headers on;
            proxy_pass_header Server;
            proxy_set_header Host $http_host;
            proxy_redirect off;
            proxy_set_header X-Real-IP $proxy_protocol_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Scheme $scheme;
            proxy_set_header X-Authed $ssl_client_verify;
            proxy_set_header ClientCert $ssl_client_s_dn;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_buffer_size          256k;
            proxy_buffers              4 512k;
            proxy_busy_buffers_size    512k;
            set $target frontends;
            access_by_lua_file /data/shared-dir/okta_redirect.lua;
            proxy_pass http://$target;
        }
    }
}