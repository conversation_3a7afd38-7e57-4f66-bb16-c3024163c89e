apiVersion: apps/v1
kind: Deployment
metadata:
  name: openresty
spec:
  template:
    spec:
      containers:
      - name: openresty
        env:
          - name: ENV
            value: "dev"
        resources:
          requests:
            cpu: "1"
            memory: 4Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: openresty-v2
spec:
  template:
    spec:
      containers:
      - name: openresty
        env:
          - name: ENV
            value: "dev"
        resources:
          requests:
            cpu: "1"
            memory: 4Gi
