apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- namespace.yaml
- ../../../base-hydra

patchesStrategicMerge:
- custom-hpa.yaml


generatorOptions:
  disableNameSuffixHash: true

#  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://app-proxy-hydra.crtx-qa2-uat.us.paloaltonetworks.com
configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_REGION=us-central1
  - APP_PROXY_PROXY_BUDDY=10.181.175.90
  - APP_PROXY_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-private-dev-01
  - APP_PROXY_SESSION_COOKIE_NAME=app-proxy-hydra-playground
  - APP_PROXY_SESSION_REF_COOKIE_NAME=app-proxy-hydra-playground-ref
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://appproxy.xdr-qa2-uat.us.paloaltonetworks.com
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_ENTITY_ID=https://application-services.qa2qa4-cortexxdr.paloaltonetworks.com
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGIN_URL=https://ssopreview.paloaltonetworks.com/app/panw-ciam_appproxycortexxdr_1/exk3vf1jn15I82s7B1d7/sso/saml
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGOUT_URL=https://ssopreview.paloaltonetworks.com/login/signout?fromURI=https://www.paloaltonetworks.com
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_SERVER_CERT_PATH=/etc/analytics/proxy/tls.crt
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_SERVER_KEY_PATH=/etc/analytics/proxy/tls.key
  - APP_PROXY_REGIONAL_DOMAIN=us.paloaltonetworks.com
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_ENTITY_ID=http://www.okta.com/exk3vf1jn15I82s7B1d7
  - APP_PROXY_IAM_SPANNER_PROJECT_ID=xdr-gateway-dev-01
  - APP_PROXY_IAM_SPANNER_INSTANCE_ID=tfgen-spanid-20201118215633241
  - APP_PROXY_PRIVATE_SPANNER_INSTANCE_ID=tfgen-spanid-20230510084159191
  - APP_PROXY_PRIVATE_SPANNER_DATABASE_ID=spanner_otp_dev
  name: appproxy-go-config
namespace: app-proxy-hydra-playground


  # todo(yfried): create as secret: random 16bytes -> base64 encode
secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
- literals:
  - cookiesecret.key=c2el3eqRkbhuTVWj7QbW4ex9QcjS4ZODcGvznsniezFKFZNLVZKFGi8uzlYlFuVv
  name: appproxy-cookiesecret


images:
- name: app-proxy
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-hf-CRTX-98143-2-v3.8.0-486-gb695e6
- name: app-proxy-hydra
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-dev-v3.8.0-283-g054cd0
- name: custom-metrics
  newName: gcr.io/kuna-224012/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: nginx
  newName: gcr.io/kuna-224012/openresty-fips
  newTag: ********-alpine
- name: nodejs
  newName: gcr.io/kuna-224012/kuna-xdr-py-appproxy
  newTag: dev-3.4.0-49-g1b8d
- name: redis
  newName: gcr.io/kuna-224012/3rd-party/redis
  newTag: 7.2.0-alpine
- name: reloader
  newName: gcr.io/kuna-224012/3rd-party/reloader
  newTag: v1.0.40
