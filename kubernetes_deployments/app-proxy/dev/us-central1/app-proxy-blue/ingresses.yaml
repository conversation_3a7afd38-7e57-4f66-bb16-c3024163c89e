apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-proxy-hydra
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/server-snippet: |-
      location ^~ /metrics {
          return 404;
      }
spec:
  rules:
    - &ingressRule
      host: appproxy.crtx-qa2-uat.us.paloaltonetworks.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: app-proxy-svc
                port:
                  name: app-proxy
    - <<: *ingressRule
      host: "*.crtx-qa2-uat.us.paloaltonetworks.com"
    - <<: *ingressRule
      host: "*.xdr-qa2-uat.us.paloaltonetworks.com"
  tls:
    - hosts:
        - "*.crtx-qa2-uat.us.paloaltonetworks.com"
      secretName: appproxy-crtx-certs
    - hosts:
        - "*.xdr-qa2-uat.us.paloaltonetworks.com"
      secretName: appproxy-certs
