apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- namespace.yaml
- ../../../base-hydra

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_REGION=us-central1
  - APP_PROXY_PROXY_BUDDY=10.167.0.146
  - APP_PROXY_SESSION_COOKIE_NAME=app-proxy-hydra-prod-fed
  - APP_PROXY_SESSION_REF_COOKIE_NAME=app-proxy-hydra-prod-fed-ref
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://app-proxy.federal.paloaltonetworks.com
  - APP_PROXY_REGIONAL_DOMAIN=federal.paloaltonetworks.com
  - APP_PROXY_PRIVATE_SPANNER_DATABASE_ID=spanner_otp_prod-fr
  - APP_PROXY_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-fr-01
  - APP_PROXY_IAM_SPANNER_PROJECT_ID=xdr-gateway-prod-fr-01
  - APP_PROXY_IAM_SPANNER_INSTANCE_ID=tfgen-spanid-20210613092642498
  - APP_PROXY_PRIVATE_SPANNER_INSTANCE_ID=tfgen-spanid-20230702080941635
  name: appproxy-go-config
- behavior: merge
  literals:
  - APP_PROXY_AUDIT_BIGQUERY_PROJECT_ID=xdr-app-proxy-prod-fr-01
  name: appproxy-go-config
namespace: app-proxy-green


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
- literals:
  - cookiesecret.key=bcC3i9kZ54xaG0McQbHC1MZs5zY0HhlCk8bgLpQUKHIjqubgTk7ONjJRrZ3KSiT3
  name: appproxy-cookiesecret


images:
- name: app-proxy
  newName: us.gcr.io/xdr-registry-prod-fr-01/agent-gateway
  newTag: hydra-master-fips-fr-v3.9.0-536-g037ee
- name: app-proxy-hydra
  newName: us.gcr.io/xdr-registry-prod-fr-01/agent-gateway
  newTag: hydra-master-fips-fr-v3.8.0-472-g39af3d
- name: appproy-init
  newName: us.gcr.io/xdr-registry-prod-fr-01/py-appproxy
  newTag: master-1.3.4-0-g2175
- name: consul-agent
  newName: us.gcr.io/xdr-registry-prod-fr-01/consul_agent
  newTag: master-v80
- name: controller
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/ingress-nginx/controller
  newTag: v1.8.0
- name: custom-metrics
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: mysql
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/mysql
  newTag: 8.0.35
- name: nginx
  newName: us.gcr.io/xdr-registry-prod-fr-01/openresty-fips
  newTag: ********-alpine
- name: nginx-ingress-controller-default-backend
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/ingress-nginx/defaultbackend-amd64
  newTag: "1.5"
- name: nodejs
  newName: us.gcr.io/xdr-registry-prod-fr-01/py-appproxy
  newTag: dev-3.4.0-49-g1b8d
- name: openresty
  newName: us.gcr.io/xdr-registry-prod-fr-01/openresty-fips
  newTag: python-apk-pip-stream
- name: prometheus
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/prometheus
  newTag: v2.47.2
- name: redis
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/redis
  newTag: 7.2.0-alpine
- name: reloader
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stakater/reloader
  newTag: v1.0.56
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
