apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: nginx-router
resources:
- ../../../nginx-router
- ../../../nginx-router/openresty-v2
- namespace.yaml

patchesStrategicMerge:
- custom-service.yaml
- custom-service-v2.yaml
- subscriber-patch.yaml
- open-resty-patch.yaml

generatorOptions:
  disableNameSuffixHash: true


configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - DEFAULT_TARGET=app-proxy-svc.app-proxy-blue.svc.cluster.local:7032
  name: targets


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
images:
- name: app-proxy
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/cortex-xdr/py-appproxy
  newTag: master-1.3.9-8-g3b6a
- name: bucket-subscriber
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/nginx-router-pubsub-subscriber
  newTag: v1-2502111834
- name: consul-agent
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/consul
  newTag: master-v85-2502101844
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/custom-metrics-stackdriver-adapter
  newTag: v0.15.1-gke.0
- name: exporter
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/google/cloud-sdk
  newTag: 512.0.0-alpine
- name: mysql
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/mysql
  newTag: 8.0.41-debian
- name: nginx
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/nginx
  newTag: 1.27.1-alpine
- name: openresty
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/openresty-fips
  newTag: ********-alpine-3.20.5-nginx-router
- name: prometheus
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/prom-tools/prometheus
  newTag: v2.47.2
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: proxy-buddy-in
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/proxy_buddy_in
  newTag: nginx-alpine-v1.27.1
- name: proxy-buddy-out
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/proxy_buddy_out
  newTag: alpine-v3.20.3
- name: redis
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/redis
  newTag: 7.4.0-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-prod-fr-01/golden-images/stakater/reloader
  newTag: v1.1.0
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-fr-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
