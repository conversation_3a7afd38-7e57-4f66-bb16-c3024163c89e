apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: nginx-router
resources:
- ../../../nginx-router
- ../../../nginx-router/openresty-v2
- namespace.yaml

patchesStrategicMerge:
- custom-service.yaml
- custom-service-v2.yaml
- subscriber-patch.yaml
- open-resty-patch.yaml

generatorOptions:
  disableNameSuffixHash: true


configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - DEFAULT_TARGET=app-proxy-svc.app-proxy-blue.svc.cluster.local:7032
  name: targets


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
images:
- name: bucket-subscriber
  newName: us.gcr.io/xdr-registry-prod-gv-01/nginx-router-pubsub-subscriber
  newTag: test
- name: consul-agent
  newName: us.gcr.io/xdr-registry-prod-gv-01/consul_agent
  newTag: master-v80
- name: custom-metrics
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/custom-metrics-stackdriver-adapter
  newTag: v0.13.1-gke.0
- name: exporter
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: mysql
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/mysql
  newTag: 8.0.35
- name: openresty
  newName: us.gcr.io/xdr-registry-prod-gv-01/openresty-fips
  newTag: python-apk-pip-stream
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
