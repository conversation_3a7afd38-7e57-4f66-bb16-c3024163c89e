apiVersion: apps/v1
kind: Deployment
metadata:
  name: openresty
spec:
  template:
    spec:
      containers:
      - name: openresty
        env:
          - name: ENV
            value: "prod-gv"
        resources:
          requests:
            cpu: "1"
            memory: 4Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: openresty-v2
spec:
  template:
    spec:
      containers:
      - name: openresty
        env:
          - name: ENV
            value: "prod-gv"
        resources:
          requests:
            cpu: "1"
            memory: 4Gi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: openresty-hpa
spec:
  maxReplicas: 3
  minReplicas: 3
