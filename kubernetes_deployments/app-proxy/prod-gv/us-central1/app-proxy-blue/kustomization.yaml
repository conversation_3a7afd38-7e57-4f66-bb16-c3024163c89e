apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- namespace.yaml
- ../../../base-hydra
- ../../../../reloader

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_REGION=us-central1
  - APP_PROXY_PROXY_BUDDY=10.167.132.7
  - APP_PROXY_SESSION_COOKIE_NAME=app-proxy-hydra-prod-gv
  - APP_PROXY_SESSION_REF_COOKIE_NAME=app-proxy-hydra-prod-gv-ref
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://app-proxy.gv.paloaltonetworks.com
  - APP_PROXY_REGIONAL_DOMAIN=gv.paloaltonetworks.com
  - APP_PROXY_PRIVATE_SPANNER_DATABASE_ID=spanner_otp_prod-gv
  - APP_PROXY_PRIVATE_FIRESTORE_PROJECT_ID=xdr-firestore-pvt-prod-gv-01
  - APP_PROXY_IAM_SPANNER_PROJECT_ID=xdr-gateway-prod-gv-01
  - APP_PROXY_PRIVATE_SPANNER_INSTANCE_ID=tfgen-spanid-20230702103128476
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_ENTITY_ID=ForgerockCustomAttributesIDP
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGIN_URL=https://sso-fed.paloaltonetworks.us/am/SSOPOST/metaAlias/ForgerockCustomAttributesIDP
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGOUT_URL=https://sso-fed.paloaltonetworks.us/am/IDPSloPOST/metaAlias/ForgerockCustomAttributesIDP
  - APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_SERVER_CERT_PATH=/etc/analytics/proxy/idp_fr_okta.pem
  name: appproxy-go-config
- behavior: merge
  literals:
  - APP_PROXY_AUDIT_BIGQUERY_PROJECT_ID=xdr-app-proxy-prod-gv-01
  name: appproxy-go-config
namespace: app-proxy-blue

patchesStrategicMerge:
- custom-dep.yaml
- reloader-patch.yaml

secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  - certs/idp_fr_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
- literals:
  - cookiesecret.key=bcC3i9kZ54xaG0McQbHC1MZs5zY0HhlCk8bgLpQUKHIjqubgTk7ONjJRrZ3KSiT3
  name: appproxy-cookiesecret


images:
- name: app-proxy
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/cortex-xdr/agent-gateway
  newTag: hydra-master-fips-gv-v3.15-458-g92bc2
- name: app-proxy-hydra
  newName: us.gcr.io/xdr-registry-prod-gv-01/agent-gateway
  newTag: hydra-master-fips-gv-v3.8.0-472-g39af3d
- name: appproy-init
  newName: us.gcr.io/xdr-registry-prod-gv-01/py-appproxy
  newTag: master-1.3.4-0-g2175
- name: bucket-subscriber
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/nginx-router-pubsub-subscriber
  newTag: v1-2502111834
- name: consul-agent
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/consul
  newTag: 1.20.2-v1
- name: controller
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/ingress-nginx/controller
  newTag: v1.8.0
- name: custom-metrics
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/custom-metrics-stackdriver-adapter
  newTag: v0.15.1-gke.0
- name: exporter
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: google-cloud-sdk
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/google/cloud-sdk
  newTag: 524.0.0-alpine
- name: mysql
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/mysql
  newTag: 8.0.41-debian
- name: nginx
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/nginx
  newTag: 1.28.0-alpine
- name: nginx-ingress-controller-default-backend
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/ingress-nginx/defaultbackend-amd64
  newTag: "1.5"
- name: nodejs
  newName: us.gcr.io/xdr-registry-prod-gv-01/py-appproxy
  newTag: dev-3.4.0-49-g1b8d
- name: openresty
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/openresty-fips
  newTag: ********-alpine-3.20.5-nginx-router
- name: prometheus-adapter
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/prom-tools/k8s-prometheus-adapter
  newTag: v0.12.0
- name: proxy-buddy-in
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/proxy_buddy_in
  newTag: nginx-alpine-v1.27.1
- name: proxy-buddy-out
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/proxy_buddy_out
  newTag: alpine-v3.20.3
- name: redis
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/redis
  newTag: 7.4.0-alpine
- name: reloader
  newName: us-docker.pkg.dev/xdr-registry-prod-gv-01/golden-images/stakater/reloader
  newTag: v1.1.0
- name: stackdriver-exporter
  newName: us.gcr.io/xdr-registry-prod-gv-01/3rd-party/stackdriver-exporter
  newTag: v0.14.1
