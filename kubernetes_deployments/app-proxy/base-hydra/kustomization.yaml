apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - service-account.yaml
  - deployment.yaml
  - services.yaml
  - hpa.yaml
  - ../../essentials
  - ../../redis
  - appproxy-go-config.yaml

generatorOptions:
  disableNameSuffixHash: true

# Generate config from templates
configMapGenerator:
- name: configmap-parameters
  envs:
  - params

secretGenerator:
- files:
  - certs/APP_PROXY_SESSION_IMPERSONATOR_COOKIE_SECRET
  name: appproxy-secrets

vars:
- name: namespace
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: metadata.namespace

- name: env
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.env

- name: mag_env
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.mag_env

- name: region
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.region

- name: sso_identity_url_domain
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.sso_identity_url_domain

- name: sso_login_url_domain
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.sso_login_url_domain

- name: sso_entity_id
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.sso_entity_id

- name: cookie_env
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.cookie_env

- name: project_id
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.project_id

- name: firestore_private_project_id
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.firestore_private_project_id

- name: firestore_public_project_id
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.firestore_public_project_id

configurations:
- params.yaml

patchesStrategicMerge:
  - custom-redis.yaml