apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    reloader.stakater.com/auto: "true"
  labels:
    app: app-proxy
    customer: panap
  name: app-proxy
spec:
  selector:
    matchLabels:
      app: app-proxy
      customer: panap
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/port: "8899"
        prometheus.io/scrape: "true"
      labels:
        app: app-proxy
        customer: panap
    spec:
      containers:
      - name: app-proxy
        command:
        - /go/bin/app_proxy
        env:
          - name: APP_PROXY_SESSION_COOKIE_HMAC_SECRET_BASE64
            valueFrom:
              secretKeyRef:
                optional: false
                name: appproxy-cookiesecret
                key: cookiesecret.key
          - name: APP_PROXY_SESSION_IMPERSONATOR_COOKIE_SECRET
            valueFrom:
              secretKeyRef:
                optional: false
                name: appproxy-secrets
                key: APP_PROXY_SESSION_IMPERSONATOR_COOKIE_SECRET
        envFrom:
        - configMapRef:
            name: appproxy-go-config
            optional: false
        image: app-proxy
        livenessProbe: &Probe
          failureThreshold: 2
          httpGet: &ProbeHttp
            path: /liveness
            port: 7032
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        ports:
        - containerPort: 7032
          protocol: TCP
          name: app-proxy
        readinessProbe:
          <<: *Probe
          httpGet:
            <<: *ProbeHttp
            path: /readiness
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
          requests:
            cpu: "1"
            memory: 4Gi
        volumeMounts:
        - mountPath: /etc/analytics/proxy/tls.key
          name: appproxy-certs
          subPath: tls.key
        - mountPath: /etc/analytics/proxy/tls.crt
          name: appproxy-certs
          subPath: tls.crt
        - mountPath: proxy.us.paloaltonetworks.com-pingid-2021.key
          name: appproxy-certs
          subPath: app_proxy_key.pem
        - mountPath: proxy.us.paloaltonetworks.com-pingid-2021.crt
          name: appproxy-certs
          subPath: app_proxy_cert.pem
        - mountPath: /data/shared-dir
          name: shared-dir
        - mountPath: /etc/analytics/proxy/idp_okta.pem
          name: appproxy-certs
          subPath: idp_okta.pem
        - mountPath: /opt/certs/jwt_key.pem
          name: appproxy-certs
          subPath: jwt_key.pem
        - mountPath: /opt/certs/jwt_cert.pem
          name: appproxy-certs
          subPath: jwt_cert.pem
        - mountPath: /etc/analytics/proxy/sp.key
          name: appproxy-certs
          subPath: proxy.us.paloaltonetworks.com-pingid-2021.key
        - mountPath: /etc/analytics/proxy/sp.crt
          name: appproxy-certs
          subPath: proxy.us.paloaltonetworks.com-pingid-2021.crt
      serviceAccountName: app-proxy
      securityContext:
        fsGroup: 888
      volumes:
      - emptyDir: {}
        name: shared-dir
      - configMap:
          defaultMode: 420
          name: appproxy-orchestration-json
        name: orchestration-json
      - configMap:
          defaultMode: 420
          name: appproxy-appproxy-conf
        name: appproxy-conf
      - name: appproxy-certs
        secret:
          defaultMode: 384
          optional: false
          secretName: appproxy-certs
