apiVersion: v1
kind: ConfigMap
metadata:
  name: appproxy-go-config
data:
  APP_PROXY_ALLOW_EMPTY_PATH: "true"
  GONZO_EXTRA_SHUTDOWN_TIMEOUT_SEC: "0"
  APP_PROXY_REDIS_CONNECTION_STRING: "redis-svc:6379"
  GONZO_REDIS_CONNECTION_STRING: "redis-svc:6379"
  GONZO_LOGGER_TRANSPORT: "console:0"
  APP_PROXY_SAML_SUPPORT_ACCOUNTS_PROPERTY: supportaccountids
  # todo(yfried): load secret
  APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_SERVER_CERT_PATH: /etc/analytics/proxy/idp_okta.pem
  APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_SERVER_CERT_PATH: /etc/analytics/proxy/tls.crt
  APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_SERVER_KEY_PATH: /etc/analytics/proxy/tls.key
#  JWTCONF_CERT: /opt/certs/jwt_cert.pem # not needed
  APP_PROXY_AUTH_JWT_RSA_PRIVATE_KEY_FILE_PATH: /opt/certs/jwt_key.pem
  # todo(yfried): consider creating a new entity ID in okta
  APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_ENTITY_ID: http://www.okta.com/exk3q6dl0bh9FOwtQ0j6
  APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_ENTITY_ID: ApolloProdUS
  APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGIN_URL: https://sso.paloaltonetworks.com/app/panw-ciam_apolloprodus_2/exk3q6dl0bh9FOwtQ0j6/sso/saml
  APP_PROXY_SAML_DEFAULT_IDENTITY_PROVIDER_LOGOUT_URL: https://sso.paloaltonetworks.com/login/signout?fromURI=https://www.paloaltonetworks.com
  APP_PROXY_AUDIT_BIGQUERY_DATASET_ID: ds_appproxy
  APP_PROXY_AUDIT_BIGQUERY_TABLE_ID: audit
  APP_PROXY_PRIVATE_FIRESTORE_PROJECT_ID: xdr-firestore-pvt-prod-us-01
  APP_PROXY_IAM_SPANNER_PROJECT_ID: xdr-gateway-prod-us-01
  APP_PROXY_IAM_SPANNER_INSTANCE_ID: tfgen-spanid-20210503092900534
  APP_PROXY_IAM_SPANNER_DATABASE_ID: xdr_gateway
  APP_PROXY_PRIVATE_SPANNER_INSTANCE_ID: tfgen-spanid-20230611090040719
  APP_PROXY_PROXY_BUDDY_TIMEOUT: 180m