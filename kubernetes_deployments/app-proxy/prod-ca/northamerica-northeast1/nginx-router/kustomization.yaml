apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: nginx-router
resources:
- ../../../nginx-router
- ../../../nginx-router/openresty-v2
- namespace.yaml

patchesStrategicMerge:
- custom-service.yaml
- custom-service-v2.yaml
- subscriber-patch.yaml
- open-resty-patch.yaml

generatorOptions:
  disableNameSuffixHash: true


configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
images:
- name: bucket-subscriber
  newName: gcr.io/kuna-224012/nginx-router-pubsub-subscriber
  newTag: test
- name: consul-agent
  newName: gcr.io/kuna-224012/consul_agent
  newTag: rm_kubectl-v78
- name: exporter
  newName: gcr.io/kuna-224012/3rd-party/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: openresty
  newName: gcr.io/kuna-224012/openresty-fips
  newTag: python-apk-pip-stream
