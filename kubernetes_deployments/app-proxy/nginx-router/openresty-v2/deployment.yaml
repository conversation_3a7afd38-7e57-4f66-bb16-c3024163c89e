apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: "nginx-conf,openresty-conf,lua-scripts"
    secret.reloader.stakater.com/reload: "appproxy-certs,appproxy-crtx-certs"
  name: openresty-v2
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: openresty-v2
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "4040"
      labels:
        app: openresty-v2
    spec:
      serviceAccountName: openresty
      containers:
      - image: openresty
        name: openresty
        command:
          - /bin/sh
          - -c
        args:
          - >
            python /home/<USER>/download_nginx_conf.py;
            /usr/local/openresty/bin/openresty -c /etc/nginx/nginx.conf
            -g "daemon off; error_log /dev/stderr info;" & /usr/sbin/incrond -n
        imagePullPolicy: IfNotPresent
        resources:
          requests:
            cpu: "3"
            memory: 9Gi
        ports:
          - containerPort: 8080
          - containerPort: 8443
          - containerPort: 8090
        livenessProbe:
         failureThreshold: 3
         httpGet:
           path: /ping/
           port: 8999
           scheme: HTTP
         periodSeconds: 15
         successThreshold: 1
         timeoutSeconds: 1
        readinessProbe:
         failureThreshold: 3
         httpGet:
           path: /ping/
           port: 8999
           scheme: HTTP
         periodSeconds: 10
         successThreshold: 1
         timeoutSeconds: 1
        envFrom:
          - configMapRef:
              name: targets
              optional: false
        volumeMounts:
        - mountPath: /etc/nginx/nginx.conf
          name: nginx-conf-v2
          subPath: nginx-v2.conf
        - mountPath: /tmp/upstreams.nginx.conf
          name: nginx-conf
          subPath: upstreams.nginx.conf
        - mountPath: /etc/incron.d/incron
          name: openresty-conf
          subPath: incron
        - mountPath: /data/shared-dir/okta_redirect.lua
          name: lua-scripts
          subPath: okta_redirect.lua
        - mountPath: /opt/nginx_reload.sh
          name: openresty-conf
          subPath: nginx_reload.sh
        - mountPath: /bucket_hash_files
          name: bucket-files-hashes
        - mountPath: /home/<USER>/download_nginx_conf.py
          name: openresty-conf-v2
          subPath: download_nginx_conf.py
        # tls terminations
        - mountPath: /etc/nginx/ssl/star.xdr.paloaltonetworks.com.pem
          name: appproxy-certs
          subPath: tls.crt
        - mountPath: /etc/nginx/ssl/star_xdr.key
          name: appproxy-certs
          subPath: tls.key
        - mountPath: /etc/nginx/ssl/proxy.paloaltonetworks.com.pem
          name: appproxy-certs
          subPath: proxy.paloaltonetworks.com.pem
        - mountPath: /etc/nginx/ssl/proxy.key
          name: appproxy-certs
          subPath: proxy.key
        - mountPath: /etc/nginx/ssl/star.crtx.paloaltonetworks.com.pem
          name: appproxy-crtx-certs
          subPath: tls.crt
        - mountPath: /etc/nginx/ssl/star_crtx.key
          name: appproxy-crtx-certs
          subPath: tls.key
      - name: exporter
        image: exporter
        args: ["-config-file", "/etc/prometheus-nginxlog-exporter/config.hcl"]
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: exporter-config
          mountPath: /etc/prometheus-nginxlog-exporter
      securityContext:
        fsGroup: 888
      volumes:
      - configMap:
          name: nginx-conf-v2
        name: nginx-conf-v2
      - configMap:
          name: openresty-conf-v2
        name: openresty-conf-v2
      - configMap:
          name: nginx-conf
        name: nginx-conf
      - configMap:
          name: openresty-conf
        name: openresty-conf
      - configMap:
          name: lua-scripts
        name: lua-scripts
      - configMap:
          name: bucket-files-hashes
        name: bucket-files-hashes
      - name: exporter-config
        configMap:
          name: exporter-config
      - name: appproxy-crtx-certs
        secret:
          defaultMode: 384
          optional: false
          secretName: appproxy-crtx-certs
      - name: appproxy-certs
        secret:
          defaultMode: 384
          optional: false
          secretName: appproxy-certs
