# NGINX Router
[JIRA](https://jira-hq.paloaltonetworks.local/browse/CRTX-88338)

This service serves similar function like nginx-ingress-controller and directs incoming HTTP request
to the proper backend (`BLUE` or `GREEN`) based on the FQDN.
It is based on the Proxy-Buddy-In design.

## Single Tenant Labeling
Each tenant has a consul key `app_proxy_target` with the matching service target.
`blue`/`green` are the default values but a custom value of `SERVICE_NAME.NAMESPACE.svc.cluster.local:PORT`
can be used to redirect the traffic to any service in the cluster.
This can be used to support the `hydra-qa` and `hydra-playground` but can be further used
for skaffold support in the future

```mermaid
graph LR
ST{Tenant}
ST-.blue/green.->CS
subgraph Shared VPC
    direction TB
    CS{{Consul Server}}
    subgraph Proxy Buddy In
        CT{{fa:fa-hexagon-xmark consul-template}}
        CS -.event.-> CT      
    end
end

subgraph App Proxy
    subgraph nginx-router
    NGINX{{nginx}}
    CM[/bucket-hashes/]
    SUB{{subscriber}}
    end
    FILES([fa:fa-database files-bucket]) --download--> NGINX
    FILES-->PUB(fa:fa-xmarks-lines pubsub)
    PUB-.->SUB
    SUB--update-->CM
    CM--mount-->NGINX
    CT --upload--> FILES
end
```


## Consul Template
The consul-template is deployed as a sidecar to the consul-agent in [shared-vpc/proxy-buddy-in](../../shared-vpc/base/consul_agent) 
This is because the consul works in mesh architecture and the agent pod can reach the consul-server pods in the shared-vpc 
cluster.

Consul agent listens to consul events and generates 2 conf files
* lua_targets.conf
* nginx_fqdn.conf

These files contain snippets that match each tenant's FQDN to the target based on the `app_proxy_target`
key. For the values `blue`/`green` the target is set from [upstream config]() 
which is included in the begining of the file.
Any other values will be written directly to file 
as mentioned above.
On each consul event, a [python script](../../shared-vpc/base/consul_agent/app_proxy_template_conf_files/upload_to_gcs.py)
is triggered and uploads the files to [GCS bucket](#gcp-bucket). 
to bucket.

In dev we added 2 extra namespaces `hydra-qa` and `hydra-playground` they are reachable by setting `qa`/`playground` 
instead of `blue`/`green`


## GCP Bucket
The generated files are uploaded to the bucket `app-proxy-nginx-router-configs-<ENV>`. The bucket also has 
a notification topic that updates when new file versions are uploaded to the bucket. 

## Pubsub Subscriber
The [subscriber pod](./pubsub_consumer) updates the `bucket-files-hashes` with the latest md5 hashes of each new file
uplodaed to the bucket.
It is in a different pod to allow the [nginx](#openresty-nginx) pod to scale independently according to traffic load. 

## Openresty Nginx
The `bucket-files-hashes` contains the sha1 values for each of the files above. This configmap is monitored by 
Openresty using `incron` to trigger reload of the generated files when changed.
The Openresty deployment monitors changes to the mounted [configmap](#Hash-Configmap) using `incron` and triggers a 
[reload script](./openresty/conf_files/nginx_reload.sh) that [downloads](./openresty/conf_files/download_nginx_conf.py)
the latest version of each file from the [bucket](#gcp-bucket) and reloads the nginx service. 

### Upstream Conf
A [dedicate file](./openresty/conf_files/upstreams.nginx.conf) is used to define the blue/green targets as nginx upstreams
based on the `targets` configmap and populated by kustomize vars. This is done because nginx doesn't support environment
variables. This file is loaded by the [nginx snippets file](../../shared-vpc/base/consul_agent/app_proxy_template_conf_files/nginx_fqdn.ctmpl)

### SSL Termination
The Openresty also provides SSL termination (similar to ingress controller).
Since AppProxyV1 has nginx sidecar that does SSL termination, the following changes were introduced:
1. Expose a new port `(9080)` that listens to non-ssl traffic
2. Add nginx configuration for this port which is identical to the old configuration, only without TLS termination.


### Lua Targets
The `lua_targets.conf` file contains a list of FQDN and TARGET pair.
Each pair is in a new line and separated by space.

After authentication, okta redirects the traffic to the main appProxy url
(`appproxy.xdr.REGSION.panw.com`) and not the original destination FQDN. 
However, the original FQDN is added to the request under the `relayState` header.
A static [nginx config](./openresty/conf_files/nginx.conf) uses [lua script](./openresty/conf_files/okta_redirect.lua)
to fetch the FQDN and look for the matching target in a generated file.
The generated file is loaded to a hash table (python dict) and cached in memory for fast access.


## Alternatives
### Ingress Controller
We aren't using ingress controller here because:
1. We already have a working implementation (Proxy-Buddy-In) and creating a dynamic ingress generator is a new adventure
2. Need to support Okta redirect with Lua scripts