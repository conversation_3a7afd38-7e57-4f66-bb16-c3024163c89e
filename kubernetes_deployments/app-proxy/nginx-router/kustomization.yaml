apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: nginx-router
resources:
  - openresty
  - pubsub_consumer

generatorOptions:
  disableNameSuffixHash: true

# Generate config from templates
configMapGenerator:
- name: bucket-files-hashes
- name: configmap-parameters
  envs:
    - params
- literals:
  - BLUE_TARGET=app-proxy-svc.app-proxy-blue.svc.cluster.local:7032
  - GREEN_TARGET=app-proxy-svc.app-proxy-green.svc.cluster.local:7032
  - DEFAULT_TARGET=app-proxy-svc.app-proxy-blue.svc.cluster.local:7032
  name: targets

vars:
- name: namespace
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: metadata.namespace

- name: region
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.region

- name: servername_base
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.servername_base

- name: base_ssl_key
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.base_ssl_key

- name: base_ssl_cert
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.base_ssl_cert

- name: project_id
  objref:
    kind: ConfigMap
    name: configmap-parameters
    apiVersion: v1
  fieldref:
    fieldpath: data.project_id

- name: blue_target
  objref:
    kind: ConfigMap
    name: targets
    apiVersion: v1
  fieldref:
    fieldpath: data.BLUE_TARGET

- name: green_target
  objref:
    kind: ConfigMap
    name: targets
    apiVersion: v1
  fieldref:
    fieldpath: data.GREEN_TARGET

- name: default_target
  objref:
    kind: ConfigMap
    name: targets
    apiVersion: v1
  fieldref:
    fieldpath: data.DEFAULT_TARGET


#configurations:
#- params.yaml