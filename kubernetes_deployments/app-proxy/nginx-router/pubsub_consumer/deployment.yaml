apiVersion: apps/v1
kind: Deployment
metadata:
  name: bucket-subscriber
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: bucket-subscriber
  template:
    metadata:
      labels:
        app: bucket-subscriber
    spec:
      serviceAccountName: openresty
      containers:
      - image: bucket-subscriber
        name: bucket-subscriber
        imagePullPolicy: IfNotPresent
        env:
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
        resources:
          requests:
            cpu: 250m
            memory: 250Mi
      securityContext:
        fsGroup: 888
