---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: bucket-subscriber
rules:
  - apiGroups: [ "" ]
    resources: [ "configmaps" ]
    verbs: [ "get", "patch" , "update" ]
    resourceNames: [ "bucket-files-hashes" ]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: bucket-subscriber
subjects:
- kind: ServiceAccount
  name: openresty
roleRef:
  kind: Role
  name: bucket-subscriber
  apiGroup: rbac.authorization.k8s.io