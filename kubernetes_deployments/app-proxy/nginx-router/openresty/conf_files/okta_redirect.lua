local targets = nil  -- Initialize an empty table for the targets
local lua_conf_file = "/data/shared-dir/lua_targets.conf"
-- todo(yfried): decide default
local defaultTarget = os.getenv("DEFAULT_TARGET")

-- Read the "lua_targets.conf" file and populate the targets table
local function loadTargets(filename)
    ngx.log(ngx.DEBUG, "Default target: " .. defaultTarget)
    local file = io.open(filename, "r")
    if file then
        targets = {}
        for line in file:lines() do
            -- Trim leading and trailing whitespace
            line = line:match("^%s*(.-)%s*$")
            -- Ignore empty lines or lines with only whitespace
            if line and line ~= "" then
                local fqdn, target = line:match("([^%s]+)%s+([^%s]+)")
                if fqdn and target then
                    targets[fqdn] = os.getenv(target) or defaultTarget
                end
            end
        end
        file:close()
        ngx.log(ngx.NOTICE, "Loaded lua targets from file: " .. lua_conf_file)
    else
        -- todo(yfried): throw exception
        ngx.log(ngx.NOTICE, "Failed to open file " .. lua_conf_file)
        return
    end
end

-- Initialize cached content upon script load or first request
if not targets then
    loadTargets(lua_conf_file)
end


local function getHostFromURL(url)
    local protocol, host, port = url:match("^(https?:)//([%w.-]+)(:?%d*)/")
    return host
end

local function getRelayState()
    if ngx.req.get_method() == "POST" then
        -- Get the POST parameters as a Lua table
        ngx.req.read_body()  -- Read the request body first
        local post_args = ngx.req.get_post_args()
        local relayState = post_args["RelayState"]
        local host = getHostFromURL(relayState)
        ngx.log(ngx.NOTICE, "@Got Host " .. host)
        return host
    end
    return nil
end



local function main()
    local relayState = getRelayState()
    if not relayState then
        ngx.log(ngx.NOTICE, "@Relay state not found")
        return
    end
    ngx.log(ngx.DEBUG, "@Relay State " .. relayState)
    local target = targets[relayState] or defaultTarget
    ngx.log(ngx.DEBUG, "Got Target " .. target)
    ngx.var.target = target
    return
end


main()