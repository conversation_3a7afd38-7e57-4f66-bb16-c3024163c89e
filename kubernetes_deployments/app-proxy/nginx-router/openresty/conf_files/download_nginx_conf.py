import os

from google.cloud import storage

ENV = os.getenv('ENV')
BUCKET_NAME = os.getenv('BUCKET_NAME', f"app-proxy-nginx-router-configs-{ENV}")

lua_targets_file = '/data/shared-dir/lua_targets.conf'
nginx_fqdn_file = '/etc/nginx/conf.d/nginx_fqdn.conf'


def download_blob(*files: str):
    storage_client = storage.Client()
    bucket = storage_client.bucket(BUCKET_NAME)
    for destination_file_name in files:
        source_blob_name = os.path.basename(destination_file_name)
        blob = bucket.blob(source_blob_name)
        blob.download_to_filename(destination_file_name)

        print(
            "Downloaded storage object {} from bucket {} to local file {}.".format(
                source_blob_name, BUCKET_NAME, destination_file_name
            )
        )


if __name__ == '__main__':
    download_blob(nginx_fqdn_file, lua_targets_file)
