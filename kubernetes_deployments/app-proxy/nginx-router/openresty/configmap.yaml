---
apiVersion: v1
kind: ConfigMap
metadata:
  name: exporter-config
data:
  config.hcl: |
    listen {
      port = 4040
    }

    namespace "access_log" {
      metrics_override = { prefix = "nginx" }
      source = {
        syslog {
          listen_address = "udp://127.0.0.1:5531"
          format = "auto"
          tags = ["nginx"]
        }
      }

      format = "$remote_addr - $remote_user [$time_local] \"$request\" $status $body_bytes_sent \"$http_referer\" \"$http_user_agent\" \"$http_x_forwarded_for\" \"$host\" \"$upstream_response_time\""

      labels {
        app = "nginx-router"
        log_source = "access_log"
      }
    }

    namespace "error_log" {
      metrics_override = { prefix = "nginx" }
      source = {
        syslog {
          listen_address = "udp://127.0.0.1:5532"
          format = "auto"
          tags = ["nginxerror"]
        }
      }

      format = "$date $time [$level] $pid#$tid: *$cid $message"

      labels {
        app = "nginx-router"
        log_source = "error_log"
      }
      relabel "level" {
        from = "level"
      }
    }
