apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - configmap.yaml
  - deployment.yaml
  - service.yaml
  - hpa.yaml
  - service-account.yaml

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- name: nginx-conf
  files:
  - conf_files/nginx.conf
  - conf_files/upstreams.nginx.conf
- name: openresty-conf
  files:
    - conf_files/download_nginx_conf.py
    - conf_files/incron
    - conf_files/nginx_reload.sh
- name: lua-scripts
  files:
  - conf_files/okta_redirect.lua

configurations:
  - params.yaml