apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- namespace.yaml
- ../../../base-hydra
- ../../../../reloader

patchesStrategicMerge:
- reloader-patch.yaml

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
- behavior: merge
  envs:
  - params
  name: configmap-parameters
- behavior: merge
  literals:
  - GONZO_REGION=asia-northeast1
  - APP_PROXY_PROXY_BUDDY=10.183.223.202
  - APP_PROXY_SESSION_COOKIE_NAME=app-proxy-hydra-prod-jp
  - APP_PROXY_SESSION_REF_COOKIE_NAME=app-proxy-hydra-prod-jp-ref
  - APP_PROXY_SAML_DEFAULT_SERVICE_PROVIDER_BASE_URL=https://app-proxy.jp.paloaltonetworks.com
  - APP_PROXY_REGIONAL_DOMAIN=jp.paloaltonetworks.com
  - APP_PROXY_PRIVATE_SPANNER_DATABASE_ID=spanner_otp_prod-jp
  name: appproxy-go-config
- behavior: merge
  literals:
  - APP_PROXY_AUDIT_BIGQUERY_PROJECT_ID=xdr-app-proxy-prod-jp-01
  name: appproxy-go-config
namespace: app-proxy-blue


secretGenerator:
- files:
  - certs/jwt_cert.pem
  - certs/jwt_key.pem
  - certs/proxy.key
  - certs/proxy.paloaltonetworks.com.pem
  - certs/xdr/tls.crt
  - certs/xdr/tls.key
  - certs/idp_okta.pem
  name: appproxy-certs
- files:
  - certs/crtx/tls.crt
  - certs/crtx/tls.key
  name: appproxy-crtx-certs
- literals:
  - cookiesecret.key=bcC3i9kZ54xaG0McQbHC1MZs5zY0HhlCk8bgLpQUKHIjqubgTk7ONjJRrZ3KSiT3
  name: appproxy-cookiesecret


images:
- name: app-proxy
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-stable-v3.8.0-441-ga67ec
- name: app-proxy-hydra
  newName: gcr.io/kuna-224012/kuna-xdr-agent-gateway
  newTag: hydra-master-v3.8.0-456-g3d2a9
- name: appproy-init
  newName: gcr.io/kuna-224012/kuna-xdr-py-appproxy
  newTag: master-1.3.4-0-g2175
- name: consul-agent
  newName: gcr.io/kuna-224012/consul_agent
  newTag: rm_kubectl-v78
- name: controller
  newName: gcr.io/kuna-224012/3rd-party/ingress-nginx/controller
  newTag: v1.8.0
- name: exporter
  newName: gcr.io/kuna-224012/3rd-party/prom-tools/prometheus-nginxlog-exporter
  newTag: v1.10.0
- name: nginx
  newName: gcr.io/kuna-224012/openresty-fips
  newTag: ********-alpine
- name: nginx-ingress-controller-default-backend
  newName: gcr.io/kuna-224012/3rd-party/ingress-nginx/defaultbackend-amd64
  newTag: "1.5"
- name: nodejs
  newName: gcr.io/kuna-224012/kuna-xdr-py-appproxy
  newTag: dev-3.4.0-49-g1b8d
- name: openresty
  newName: gcr.io/kuna-224012/openresty-fips
  newTag: python-apk-pip-stream
- name: redis
  newName: gcr.io/kuna-224012/redis
- name: reloader
  newName: gcr.io/kuna-224012/stakater/reloader
  newTag: v0.0.49
